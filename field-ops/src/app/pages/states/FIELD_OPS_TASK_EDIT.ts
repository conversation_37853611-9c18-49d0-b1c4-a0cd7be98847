import { bbbeee, StateConfig, validationRegex } from '@4-sure/ui-platform';

/*
 * SECTION: TASKS/EDIT STATE
 * Tasks edit state section where tasks can be reviewed in detail and actioned
 */
// #region TASKS/EDIT STATE
export const FIELD_OPS_TASK_EDIT_STATE = {
  title: { template: 'field-ops-task-edit' },
  fetchCalls: [
    // #region TASKS EDIT STATE FETCHCALLS
    // fetchcalls that will be made the state level
    {
      key: 'sp_enums',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_enum',
      body: { enum: 'all' },
      slicePath: 'payload',
    },
    // TODO: additional logic to be added to determine nature of task and respective api call
    {
      key: 'task_details',
      method: 'POST',
      url: '{VITE_SP_SERVER}/api/v1/task_actions/get_task_details',
      slicePath: 'payload',
      successFetchCalls: [
        {
          key: 'sp_profile',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_sp',
          body: { sp_id: '$object_id' },
          slicePath: 'payload',
        },
        {
          key: 'company_documentation',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/file_actions/get_files',
          body: {
            with_thumbnails: true,
            sp_id: '$object_id',
            order: '-',
          },
          slicePath: 'payload',
        },
      ],
    },
  ],
  // #endregion
  defaultScreen: 'details',
  screens: {
    // #region TASKS/EDIT/DETAILS SCREEN
    details: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
          // layout: { width: 'calc(100% - 226px - 56px)' },
        },
        {
          component: 'ProfileHero',
          layout: {
            display: 'grid',
            justifyContent: 'center',
            // width: 'calc(100% - 226px - 56px)',
          },
          props: {
            fullname: '$sp_profile.details.name',
            subText: 'Registration number: ',
            username: '$sp_profile.details.co_reg',
            active: false,
            image: '$sp_profile.company_profile_picture',
            profileType: 'company',
            state: '$sp_profile.details.onboarding_state',
            showImgUpload: false,
          },
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Company details',
                options: {
                  format: 'heading',
                  type: 'section-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            // width: 'calc(100% - 226px - 56px)',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            bbbeee_certificate:
              "$company_documentation?find:item.purpose === 'Reg BBEEE certificate'",
            defaultValues: {
              trading_as: '$sp_profile.details.trading_as',
              bbeee: '$sp_profile.details.bbeee',
              co_reg: '$sp_profile.details.co_reg',
              name: '$sp_profile.details.name',
              company_type: '$sp_profile.details.company_type',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'co_reg',
                  label: 'Company registration',
                  fieldAccessPath: {
                    view: 'details',
                    edit: 'details',
                    special: 'details',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'Registration number is required',
                    },
                    conditional: {
                      value: `$store.formDataRaw.company_type === 2 ? 'registration_number' : 'company_registration_number'`,
                      message: 'Registration number is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'name',
                  label: 'Company name',
                  fieldAccessPath: {
                    view: 'details',
                    edit: 'details',
                    special: 'details',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'Name is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'trading_as',
                  label: 'Trading as',
                  fieldAccessPath: {
                    view: 'details',
                    edit: 'details',
                    special: 'details',
                  },
                  validation: {},
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'company_type',
                  label: 'Type of company',
                  valueProp: 'id',
                  labelProp: 'name',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.company_types',
                  },
                  fieldAccessPath: {
                    view: 'details',
                    edit: 'details',
                    special: 'details',
                  },
                  validation: {},
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  dropdownScroll: true,
                  name: 'bbeee',
                  label: 'BBBEEE Level',
                  labelProp: 'label',
                  valueProp: 'value',
                  fieldAccessPath: {
                    view: 'details',
                    edit: 'details',
                    special: 'details',
                  },
                  validation: {},
                  options: {
                    data: bbbeee,
                    source: 'literal',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'bbbeee_certificate',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg BBEEE certificate',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            // width: 'calc(100% - 226px - 56px)',
          },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && (Object.keys($formState.errors).length === 0)',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                              You are about to exit this page without saving.
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                              Would you like to save or Clear your changes?
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName: 'trading_as',
                                              defaultValue:
                                                '$sp_profile.details.trading_as',
                                            },
                                            {
                                              fieldName: 'bbeee',
                                              defaultValue:
                                                '$sp_profile.details.bbeee',
                                            },
                                            {
                                              fieldName: 'co_reg',
                                              defaultValue:
                                                '$sp_profile.details.co_reg',
                                            },
                                            {
                                              fieldName: 'name',
                                              defaultValue:
                                                '$sp_profile.details.name',
                                            },
                                            {
                                              fieldName: 'company_type',
                                              defaultValue:
                                                '$sp_profile.details.company_type',
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                            sp_id: '$sp_profile.id',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/field-ops/tasks/list-view',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks/list-view'],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '$store?.postData && !(Object.keys($store.postData).length > 0)',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'trading_as',
                    defaultValue: '$sp_profile.details.trading_as',
                  },
                  {
                    fieldName: 'bbeee',
                    defaultValue: '$sp_profile.details.bbeee',
                  },
                  {
                    fieldName: 'co_reg',
                    defaultValue: '$sp_profile.details.co_reg',
                  },
                  {
                    fieldName: 'name',
                    defaultValue: '$sp_profile.details.name',
                  },
                  {
                    fieldName: 'company_type',
                    defaultValue: '$sp_profile.details.company_type',
                  },
                  {
                    fieldName: 'contact_primary',
                    defaultValue: '$sp_profile.address.contact_primary',
                  },
                  {
                    fieldName: 'contact_secondary',
                    defaultValue: '$sp_profile.address.contact_secondary',
                  },
                  {
                    fieldName: 'contact_person',
                    defaultValue: '$sp_profile.address.contact_person',
                  },
                  {
                    fieldName: 'email_receiving',
                    defaultValue: '$sp_profile.address.email_receiving',
                  },
                  {
                    fieldName: 'physical_addr',
                    defaultValue: '$sp_profile.address.physical_addr',
                  },
                  {
                    fieldName: 'physical_city',
                    defaultValue: '$sp_profile.address.physical_city',
                  },
                  {
                    fieldName: 'physical_code',
                    defaultValue: '$sp_profile.address.physical_code',
                  },
                  {
                    fieldName: 'physical_suburb',
                    defaultValue: '$sp_profile.address.physical_suburb',
                  },
                  {
                    fieldName: 'province',
                    defaultValue: '$sp_profile.address.province',
                  },
                  {
                    fieldName: 'postal_code',
                    defaultValue: '$sp_profile.address.postal_code',
                  },
                  {
                    fieldName: 'postal_box',
                    defaultValue: '$sp_profile.address.postal_box',
                  },
                  {
                    fieldName: 'postal_city',
                    defaultValue: '$sp_profile.address.postal_city',
                  },
                  {
                    fieldName: 'skills',
                    defaultValue: '$sp_profile.skills',
                  },
                  {
                    fieldName: 'after_hours',
                    defaultValue: '$sp_profile.after_hours',
                  },
                  {
                    fieldName: 'companies',
                    defaultValue: '$sp_profile.comapanies',
                  },
                  {
                    fieldName: 'accredition',
                    defaultValue:
                      '$sp_profile.additional_identities.accredition',
                  },
                  {
                    fieldName: 'mid',
                    defaultValue: '$sp_profile.additional_identities.mid',
                  },
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                  {
                    fieldName: 'b_acc_holder',
                    defaultValue: '$sp_profile.financial.b_acc_holder',
                  },
                  {
                    fieldName: 'b_branch_name',
                    defaultValue: '$sp_profile.financial.b_branch_name',
                  },
                  {
                    fieldName: 'b_acc_type',
                    defaultValue: '$sp_profile.financial.b_acc_type',
                  },
                  {
                    fieldName: 'b_branch_code',
                    defaultValue: '$sp_profile.financial.b_branch_code',
                  },
                  {
                    fieldName: 'b_bank_name',
                    defaultValue: '$sp_profile.financial.b_bank_name',
                  },
                  {
                    fieldName: 'b_acc_no',
                    defaultValue: '$sp_profile.financial.b_acc_no',
                  },
                  {
                    fieldName: 'vat_no',
                    defaultValue: '$sp_profile.financial.vat_no',
                  },
                  {
                    fieldName: 'tax_no',
                    defaultValue: '$sp_profile.financial.tax_no',
                  },
                ],
              },
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '!$store?.postData || !(Object.keys($store.postData).length > 0) || (Object.keys($formState.errors).length > 0)|| !$formState.isDirty',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndFetch',
              payload: [
                {
                  postData: '$postData',
                  sp_id: '$sp_profile.id',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                  headers: {},
                  bodySlicePath: 'postData',
                },
                { method: 'post', action: '/field-ops/tasks/edit/banking' },
              ],
            },
          ],
        },
        {
          label: 'Next',
          // disabledWhen: "$form.bbbeee_level === 'hello'",
          position: 'right',
          toScreen: '../banking',
        },
      ],
    },
    // #endregion
    // #region TASKS/EDIT/CONTACT SCREEN
    contact: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Company contact information',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              contact_primary: '$sp_profile.address.contact_primary',
              contact_secondary: '$sp_profile.address.contact_secondary',
              contact_person: '$sp_profile.address.contact_person',
              email_receiving: '$sp_profile.address.email_receiving',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'contact_person',
                  label: 'Primary Contact Person Name',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'contact_primary',
                  label: 'Primary Contact Number',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.phone_number.pattern,
                      message: validationRegex.phone_number.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'contact_secondary',
                  label: 'Secondary Contact Number',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    pattern: {
                      value: validationRegex.phone_number.pattern,
                      message: validationRegex.phone_number.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'email_receiving',
                  label: 'Email Address',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.email.pattern,
                      message: validationRegex.email.message,
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
        {
          component: 'Separator',
          layout: { width: '50%', minWidth: '712px', margin: 'auto' },
          props: { height: 'thin' },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              physical_addr: '$sp_profile.address.physical_addr',
              physical_city: '$sp_profile.address.physical_city',
              physical_code: '$sp_profile.address.physical_code',
              physical_suburb: '$sp_profile.address.physical_suburb',
              province: '$sp_profile.address.province',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'physical_addr',
                  label: 'Company street address',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'physical_suburb',
                  label: 'Company suburb',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'physical_city',
                  label: 'Company city',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'physical_code',
                  label: 'Company postal code',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'province',
                  label: 'Company province',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  dropdownScroll: true,
                  validation: {
                    required: {
                      value: true,
                      message: 'Province is required',
                    },
                  },
                  labelProp: 'name',
                  valueProp: 'name',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.provinces',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
        {
          component: 'Separator',
          layout: { width: '50%', minWidth: '712px', margin: 'auto' },
          props: { height: 'thin' },
        },
        {
          component: 'FormBuilder',
          props: {
            defaultValues: {
              postal_box: '$sp_profile.address.postal_box',
              postal_city: '$sp_profile.address.postal_city',
              postal_code: '$sp_profile.address.postal_code',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'postal_box',
                  label: 'Postal address',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'postal_city',
                  label: 'Postal address suburb/town',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'postal_code',
                  label: 'Postal address postal code',
                  fieldAccessPath: {
                    view: 'address',
                    edit: 'address',
                    special: 'address',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          // toScreen: '/',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                              You are about to exit this page without saving.
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                              Would you like to save or Clear your changes?
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName: 'contact_primary',
                                              defaultValue:
                                                '$sp_profile.address.contact_primary',
                                            },
                                            {
                                              fieldName: 'contact_secondary',
                                              defaultValue:
                                                '$sp_profile.address.contact_secondary',
                                            },
                                            {
                                              fieldName: 'contact_person',
                                              defaultValue:
                                                '$sp_profile.address.contact_person',
                                            },
                                            {
                                              fieldName: 'email_receiving',
                                              defaultValue:
                                                '$sp_profile.address.email_receiving',
                                            },
                                            {
                                              fieldName: 'physical_addr',
                                              defaultValue:
                                                '$sp_profile.address.physical_addr',
                                            },
                                            {
                                              fieldName: 'physical_city',
                                              defaultValue:
                                                '$sp_profile.address.physical_city',
                                            },
                                            {
                                              fieldName: 'physical_code',
                                              defaultValue:
                                                '$sp_profile.address.physical_code',
                                            },
                                            {
                                              fieldName: 'physical_suburb',
                                              defaultValue:
                                                '$sp_profile.address.physical_suburb',
                                            },
                                            {
                                              fieldName: 'province',
                                              defaultValue:
                                                '$sp_profile.address.province',
                                            },
                                            {
                                              fieldName: 'postal_code',
                                              defaultValue:
                                                '$sp_profile.address.postal_code',
                                            },
                                            {
                                              fieldName: 'postal_box',
                                              defaultValue:
                                                '$sp_profile.address.postal_box',
                                            },
                                            {
                                              fieldName: 'postal_city',
                                              defaultValue:
                                                '$sp_profile.address.postal_city',
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                            sp_id: '$sp_profile.id',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/field-ops/tasks/list-view',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks/list-view'],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../directors',
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '!$store?.postData || !(Object.keys($store.postData).length > 0) || (Object.keys($formState.errors).length > 0)|| !$formState.isDirty',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'trading_as',
                    defaultValue: '$sp_profile.details.trading_as',
                  },
                  {
                    fieldName: 'bbeee',
                    defaultValue: '$sp_profile.details.bbeee',
                  },
                  {
                    fieldName: 'co_reg',
                    defaultValue: '$sp_profile.details.co_reg',
                  },
                  {
                    fieldName: 'name',
                    defaultValue: '$sp_profile.details.name',
                  },
                  {
                    fieldName: 'company_type',
                    defaultValue: '$sp_profile.details.company_type',
                  },
                  {
                    fieldName: 'contact_primary',
                    defaultValue: '$sp_profile.address.contact_primary',
                  },
                  {
                    fieldName: 'contact_secondary',
                    defaultValue: '$sp_profile.address.contact_secondary',
                  },
                  {
                    fieldName: 'contact_person',
                    defaultValue: '$sp_profile.address.contact_person',
                  },
                  {
                    fieldName: 'email_receiving',
                    defaultValue: '$sp_profile.address.email_receiving',
                  },
                  {
                    fieldName: 'physical_addr',
                    defaultValue: '$sp_profile.address.physical_addr',
                  },
                  {
                    fieldName: 'physical_city',
                    defaultValue: '$sp_profile.address.physical_city',
                  },
                  {
                    fieldName: 'physical_code',
                    defaultValue: '$sp_profile.address.physical_code',
                  },
                  {
                    fieldName: 'physical_suburb',
                    defaultValue: '$sp_profile.address.physical_suburb',
                  },
                  {
                    fieldName: 'province',
                    defaultValue: '$sp_profile.address.province',
                  },
                  {
                    fieldName: 'postal_code',
                    defaultValue: '$sp_profile.address.postal_code',
                  },
                  {
                    fieldName: 'postal_box',
                    defaultValue: '$sp_profile.address.postal_box',
                  },
                  {
                    fieldName: 'postal_city',
                    defaultValue: '$sp_profile.address.postal_city',
                  },
                  {
                    fieldName: 'skills',
                    defaultValue: '$sp_profile.skills',
                  },
                  {
                    fieldName: 'after_hours',
                    defaultValue: '$sp_profile.after_hours',
                  },
                  {
                    fieldName: 'companies',
                    defaultValue: '$sp_profile.comapanies',
                  },
                  {
                    fieldName: 'accredition',
                    defaultValue:
                      '$sp_profile.additional_identities.accredition',
                  },
                  {
                    fieldName: 'mid',
                    defaultValue: '$sp_profile.additional_identities.mid',
                  },
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                  {
                    fieldName: 'b_acc_holder',
                    defaultValue: '$sp_profile.financial.b_acc_holder',
                  },
                  {
                    fieldName: 'b_branch_name',
                    defaultValue: '$sp_profile.financial.b_branch_name',
                  },
                  {
                    fieldName: 'b_acc_type',
                    defaultValue: '$sp_profile.financial.b_acc_type',
                  },
                  {
                    fieldName: 'b_branch_code',
                    defaultValue: '$sp_profile.financial.b_branch_code',
                  },
                  {
                    fieldName: 'b_bank_name',
                    defaultValue: '$sp_profile.financial.b_bank_name',
                  },
                  {
                    fieldName: 'b_acc_no',
                    defaultValue: '$sp_profile.financial.b_acc_no',
                  },
                  {
                    fieldName: 'vat_no',
                    defaultValue: '$sp_profile.financial.vat_no',
                  },
                  {
                    fieldName: 'tax_no',
                    defaultValue: '$sp_profile.financial.tax_no',
                  },
                ],
              },
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '$store?.postData && !(Object.keys($store.postData).length > 0)',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndFetch',
              payload: [
                {
                  postData: '$postData',
                  sp_id: '$sp_profile.id',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                  headers: {},
                  bodySlicePath: 'postData',
                },
                { method: 'post', action: '/field-ops/tasks/edit/work' },
              ],
            },
          ],
        },
        { label: 'Next', position: 'right', toScreen: '../work' },
      ],
    },
    // #endregion
    // #region TASKS/EDIT/WORK SCREEN
    work: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Scope of work you accept',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            sp_associated_companies: '$sp_profile.companies?map:item.client_id',
            defaultValues: {
              skills: '$sp_profile.skills',
              after_hours: '$sp_profile.after_hours',
              // mid: '$sp_profile.additional_identities.mid',
              // accredition: '$sp_profile.additional_identities.accredition',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'multiselect-checklist',
                  name: 'skills',
                  label: 'Company skills',
                  state: 'display-only',
                  fieldAccessPath: {
                    view: 'skills',
                    edit: 'skills',
                    special: 'skills',
                  },
                  labelProp: 'name',
                  valueProp: 'id',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.skills',
                  },
                  checkedItems: 'sp_profile.skills',
                  maxColumns: 2,
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'radio-group',
                  name: 'after_hours',
                  fieldAccessPath: {
                    view: 'afterhours',
                    edit: 'afterhours',
                    special: 'afterhours',
                  },
                  label: 'Do you work after hours?',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  options: {
                    source: 'literal',
                    data: [
                      { label: 'Yes', value: true },
                      { label: 'No', value: false },
                    ],
                  },
                  size: 'small',
                  returnBoolean: true,
                  css: {
                    wrapper: {
                      justifySelf: 'start',
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'multiselect-checklist',
                  name: 'sp_associated_companies',
                  companies: '$sp_profile.companies',
                  fieldAccessPath: {
                    view: 'serviceproviderclient',
                    edit: 'serviceproviderclient',
                    special: 'serviceproviderclient',
                  },
                  fieldAccessKey: 'client',
                  label: 'Companies you would like to recieve work from',
                  state: 'display-only',
                  labelProp: 'name',
                  valueProp: 'id',
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.companies',
                  },
                  checkedItems: 'sp_profile.companies',
                  checkedItemsTransformPath: 'client_id',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                // {
                //   type: 'plain-text',
                //   name: 'mid',
                //   label: 'Standard Insurance (SIL) MID',
                //   icon: 'alarm-clock',
                //   state: 'display-only',
                //   position: 'right',
                //   css: {
                //     wrapper: {
                //       height: '4rem',
                //       width: '100%',
                //     },
                //   },
                // },
                // {
                //   type: 'plain-text',
                //   name: 'accredition',
                //   label: 'Multichoice accredition number',
                //   icon: 'alarm-clock',
                //   state: 'display-only',
                //   position: 'right',
                //   css: {
                //     wrapper: {
                //       height: '4rem',
                //       width: '100%',
                //     },
                //   },
                // },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          // toScreen: '/',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                              You are about to exit this page without saving.
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                              Would you like to save or Clear your changes?
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName: 'skills',
                                              defaultValue:
                                                '$sp_profile.skills',
                                            },
                                            {
                                              fieldName: 'after_hours',
                                              defaultValue:
                                                '$sp_profile.after_hours',
                                            },
                                            {
                                              fieldName: 'companies',
                                              defaultValue:
                                                '$sp_profile.comapanies',
                                            },
                                            {
                                              fieldName: 'accredition',
                                              defaultValue:
                                                '$sp_profile.additional_identities.accredition',
                                            },
                                            {
                                              fieldName: 'mid',
                                              defaultValue:
                                                '$sp_profile.additional_identities.mid',
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                            sp_id: '$sp_profile.id',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/field-ops/tasks/list-view',
                                          },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'clearStore',
                                        payload: ['task_details', 'sp_profile'],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks/list-view'],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../contact',
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '$store?.postData && !(Object.keys($store.postData).length > 0)',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'trading_as',
                    defaultValue: '$sp_profile.details.trading_as',
                  },
                  {
                    fieldName: 'bbeee',
                    defaultValue: '$sp_profile.details.bbeee',
                  },
                  {
                    fieldName: 'co_reg',
                    defaultValue: '$sp_profile.details.co_reg',
                  },
                  {
                    fieldName: 'name',
                    defaultValue: '$sp_profile.details.name',
                  },
                  {
                    fieldName: 'company_type',
                    defaultValue: '$sp_profile.details.company_type',
                  },
                  {
                    fieldName: 'contact_primary',
                    defaultValue: '$sp_profile.address.contact_primary',
                  },
                  {
                    fieldName: 'contact_secondary',
                    defaultValue: '$sp_profile.address.contact_secondary',
                  },
                  {
                    fieldName: 'contact_person',
                    defaultValue: '$sp_profile.address.contact_person',
                  },
                  {
                    fieldName: 'email_receiving',
                    defaultValue: '$sp_profile.address.email_receiving',
                  },
                  {
                    fieldName: 'physical_addr',
                    defaultValue: '$sp_profile.address.physical_addr',
                  },
                  {
                    fieldName: 'physical_city',
                    defaultValue: '$sp_profile.address.physical_city',
                  },
                  {
                    fieldName: 'physical_code',
                    defaultValue: '$sp_profile.address.physical_code',
                  },
                  {
                    fieldName: 'physical_suburb',
                    defaultValue: '$sp_profile.address.physical_suburb',
                  },
                  {
                    fieldName: 'province',
                    defaultValue: '$sp_profile.address.province',
                  },
                  {
                    fieldName: 'postal_code',
                    defaultValue: '$sp_profile.address.postal_code',
                  },
                  {
                    fieldName: 'postal_box',
                    defaultValue: '$sp_profile.address.postal_box',
                  },
                  {
                    fieldName: 'postal_city',
                    defaultValue: '$sp_profile.address.postal_city',
                  },
                  {
                    fieldName: 'skills',
                    defaultValue: '$sp_profile.skills',
                  },
                  {
                    fieldName: 'after_hours',
                    defaultValue: '$sp_profile.after_hours',
                  },
                  {
                    fieldName: 'companies',
                    defaultValue: '$sp_profile.comapanies',
                  },
                  {
                    fieldName: 'accredition',
                    defaultValue:
                      '$sp_profile.additional_identities.accredition',
                  },
                  {
                    fieldName: 'mid',
                    defaultValue: '$sp_profile.additional_identities.mid',
                  },
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                  {
                    fieldName: 'b_acc_holder',
                    defaultValue: '$sp_profile.financial.b_acc_holder',
                  },
                  {
                    fieldName: 'b_branch_name',
                    defaultValue: '$sp_profile.financial.b_branch_name',
                  },
                  {
                    fieldName: 'b_acc_type',
                    defaultValue: '$sp_profile.financial.b_acc_type',
                  },
                  {
                    fieldName: 'b_branch_code',
                    defaultValue: '$sp_profile.financial.b_branch_code',
                  },
                  {
                    fieldName: 'b_bank_name',
                    defaultValue: '$sp_profile.financial.b_bank_name',
                  },
                  {
                    fieldName: 'b_acc_no',
                    defaultValue: '$sp_profile.financial.b_acc_no',
                  },
                  {
                    fieldName: 'vat_no',
                    defaultValue: '$sp_profile.financial.vat_no',
                  },
                  {
                    fieldName: 'tax_no',
                    defaultValue: '$sp_profile.financial.tax_no',
                  },
                ],
              },
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '!$store?.postData || !(Object.keys($store.postData).length > 0) || (Object.keys($formState.errors).length > 0)|| !$formState.isDirty',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndFetch',
              payload: [
                {
                  postData: '$postData',
                  sp_id: '$sp_profile.id',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                  headers: {},
                  bodySlicePath: 'postData',
                },
                { method: 'post', action: '/field-ops/tasks/edit/areas' },
              ],
            },
          ],
        },
        { label: 'Next', position: 'right', toScreen: '../areas' },
      ],
    },
    // #endregion
    // #region TASKS/EDIT/AREAS SCREEN
    areas: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'FormBuilder',
          layout: {
            paddingTop: '2rem',
          },
          props: {
            operational_area: '$sp_profile.operational_area',
            defaultValues: {
              radius: '$sp_profile.operational_area.0.operating_range',
              jobLocation: '$sp_profile.operational_area.0.location',
            },
            config: {
              style: {
                display: 'grid',
                gridTemplateColumns: '1fr',
                rowGap: '15px',
                justifyItems: 'center',
              },
              controls: [
                {
                  type: 'radius', // Custom control type for OperationAreas
                  name: 'operational_area',
                  label: 'Operational Area',
                  instructions: 'Drag the pin to adjust your work area radius',
                  fieldAccessPath: {
                    view: 'operational_area',
                    edit: 'operational_area',
                    special: 'operational_area',
                  },
                  fieldAccessKey: 'location',
                  // radius: 900,
                  // jobLocation: { lat: 40.712776, lng: -74.005974 },
                  marks: [
                    { value: 25, label: '25km' },
                    { value: 50, label: '50km' },
                    { value: 75, label: '75km' },
                    { value: 100, label: '100km' },
                    { value: 150, label: '150km' },
                    { value: 200, label: '200km' },
                    { value: 250, label: '250km' },
                  ],
                  // operations: {
                  //   source: 'literal',
                  //   data: [
                  //     { label: '10km', value: 10000 },
                  //     { label: '20km', value: 20000 },
                  //   ],
                  // },
                  css: { wrapper: { gridColumn: '1', gridRow: '1' } },
                },
              ],
            },
          },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          // toScreen: '/',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                              You are about to exit this page without saving.
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                              Would you like to save or Clear your changes?
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName: 'operational_area',
                                              defaultValue:
                                                '$sp_profile.operational_area',
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndNavigate',
                                        payload: [
                                          {
                                            operational_area:
                                              '$formDataRaw.operational_area',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            redirect:
                                              '/field-ops/tasks/list-view',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/field-ops/tasks/edit/areas',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks/list-view'],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../work',
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '($store?.postData && !(Object.keys($store.postData).length > 0)) && !$store.updatedOperationalArea',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'trading_as',
                    defaultValue: '$sp_profile.details.trading_as',
                  },
                  {
                    fieldName: 'bbeee',
                    defaultValue: '$sp_profile.details.bbeee',
                  },
                  {
                    fieldName: 'co_reg',
                    defaultValue: '$sp_profile.details.co_reg',
                  },
                  {
                    fieldName: 'name',
                    defaultValue: '$sp_profile.details.name',
                  },
                  {
                    fieldName: 'company_type',
                    defaultValue: '$sp_profile.details.company_type',
                  },
                  {
                    fieldName: 'contact_primary',
                    defaultValue: '$sp_profile.address.contact_primary',
                  },
                  {
                    fieldName: 'contact_secondary',
                    defaultValue: '$sp_profile.address.contact_secondary',
                  },
                  {
                    fieldName: 'contact_person',
                    defaultValue: '$sp_profile.address.contact_person',
                  },
                  {
                    fieldName: 'email_receiving',
                    defaultValue: '$sp_profile.address.email_receiving',
                  },
                  {
                    fieldName: 'physical_addr',
                    defaultValue: '$sp_profile.address.physical_addr',
                  },
                  {
                    fieldName: 'physical_city',
                    defaultValue: '$sp_profile.address.physical_city',
                  },
                  {
                    fieldName: 'physical_code',
                    defaultValue: '$sp_profile.address.physical_code',
                  },
                  {
                    fieldName: 'physical_suburb',
                    defaultValue: '$sp_profile.address.physical_suburb',
                  },
                  {
                    fieldName: 'province',
                    defaultValue: '$sp_profile.address.province',
                  },
                  {
                    fieldName: 'postal_code',
                    defaultValue: '$sp_profile.address.postal_code',
                  },
                  {
                    fieldName: 'postal_box',
                    defaultValue: '$sp_profile.address.postal_box',
                  },
                  {
                    fieldName: 'postal_city',
                    defaultValue: '$sp_profile.address.postal_city',
                  },
                  {
                    fieldName: 'skills',
                    defaultValue: '$sp_profile.skills',
                  },
                  {
                    fieldName: 'after_hours',
                    defaultValue: '$sp_profile.after_hours',
                  },
                  {
                    fieldName: 'companies',
                    defaultValue: '$sp_profile.comapanies',
                  },
                  {
                    fieldName: 'accredition',
                    defaultValue:
                      '$sp_profile.additional_identities.accredition',
                  },
                  {
                    fieldName: 'mid',
                    defaultValue: '$sp_profile.additional_identities.mid',
                  },
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                  {
                    fieldName: 'b_acc_holder',
                    defaultValue: '$sp_profile.financial.b_acc_holder',
                  },
                  {
                    fieldName: 'b_branch_name',
                    defaultValue: '$sp_profile.financial.b_branch_name',
                  },
                  {
                    fieldName: 'b_acc_type',
                    defaultValue: '$sp_profile.financial.b_acc_type',
                  },
                  {
                    fieldName: 'b_branch_code',
                    defaultValue: '$sp_profile.financial.b_branch_code',
                  },
                  {
                    fieldName: 'b_bank_name',
                    defaultValue: '$sp_profile.financial.b_bank_name',
                  },
                  {
                    fieldName: 'b_acc_no',
                    defaultValue: '$sp_profile.financial.b_acc_no',
                  },
                  {
                    fieldName: 'vat_no',
                    defaultValue: '$sp_profile.financial.vat_no',
                  },
                  {
                    fieldName: 'tax_no',
                    defaultValue: '$sp_profile.financial.tax_no',
                  },
                ],
              },
            },
            {
              type: 'clientAction',
              action: 'clearStore',
              payload: ['updatedOperationalArea'],
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '!$store?.postData || !(Object.keys($store.postData).length > 0) || (Object.keys($formState.errors).length > 0)|| !$formState.isDirty',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndNavigate',
              payload: [
                {
                  operational_area: '$formDataRaw.operational_area',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                  headers: {},
                  redirect: '/field-ops/tasks/edit/documentation',
                },
                { method: 'post', action: '/field-ops/tasks/edit/areas' },
              ],
            },
          ],
        },
        { label: 'Next', position: 'right', toScreen: '../documentation' },
      ],
    },
    // #endregion
    // #region TASKS/EDIT/DOCUMENTATION SCREEN
    documentation: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Uploaded documents',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'PDF only. Maximum 5Mb per document',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'DocumentCardList',
          props: {
            documents: `js:{
            const skill_certifications = company_documentation
                .filter(item => !!item.meta.certification)
                .reduce((acc, item) => {
                  const cert = item.meta.certification;
                  const key = cert.skill+"-"+cert.issuer;
                  if (!acc.has(key) || new Date(item.created) > new Date(acc.get(key).created)) {
                    acc.set(key, item);
                  }

                  return acc;
                }, new Map())
                .values();
              const certificatesArray = Array.from(skill_certifications);
              if(typeof certificatesArray !== 'object' && !Array.isArray(certificatesArray)) return []
              return certificatesArray || [];
            }`,
            isLandscape: true,
            sp_id: '$sp_profile.id',
            base_url_env_name: 'VITE_SP_SERVER',
            director_id: '$director.id',
            // file_download_url: '{VITE_SP_SERVER}/api/v1/file_actions/download_file',
            columns: 1,
            gap: '1rem',
          },
          layout: {
            alignSelf: 'center',
            paddingTop: '1rem',
            minWidth: '712px',
            width: '50%',
            justifySelf: 'center',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            co_reg_document:
              "$company_documentation?find:item.purpose === 'Reg Company registration'",
            pub_profile_document:
              "$company_documentation?find:item.purpose === 'Reg Public profile'",
            pub_liability_document:
              "$company_documentation?find:item.purpose === 'Reg Public liability insurance'",
            bbeee_cert_document:
              "$company_documentation?find:item.purpose === 'Reg BBEEE certificate'",
            sars_tax_document:
              "$company_documentation?find:item.purpose === 'Reg SARS Tax certificate'",
            proof_of_bank_account_document:
              "$company_documentation?find:item.purpose === 'Reg Proof of bank account'",
            vehicle_document:
              "$company_documentation?find:item.purpose === 'Reg Vehicle picture'",
            office_document:
              "$company_documentation?find:item.purpose === 'Reg Office picture'",
            staff_uniform_document:
              "$company_documentation?find:item.purpose === 'Reg Staff uniform picture'",
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
              },
              controls: [
                {
                  type: 'document-card',
                  name: 'co_reg_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Company registration',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'pub_profile_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Public profile',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'pub_liability_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Public liability insurance',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'bbeee_cert_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg BBEEE certificate',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'sars_tax_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg SARS Tax certificate',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'proof_of_bank_account_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Proof of bank account',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'vehicle_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Vehicle picture',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'office_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Office picture',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'staff_uniform_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Staff uniform picture',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            // width: 'calc(100% - 226px - 56px)',
          },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          // toScreen: '/',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                              You are about to exit this page without saving.
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                              Would you like to save or Clear your changes?
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName: 'b_acc_holder',
                                              defaultValue:
                                                '$sp_profile.financial.b_acc_holder',
                                            },
                                            {
                                              fieldName: 'b_branch_name',
                                              defaultValue:
                                                '$sp_profile.financial.b_branch_name',
                                            },
                                            {
                                              fieldName: 'b_acc_type',
                                              defaultValue:
                                                '$sp_profile.financial.b_acc_type',
                                            },
                                            {
                                              fieldName: 'b_branch_code',
                                              defaultValue:
                                                '$sp_profile.financial.b_branch_code',
                                            },
                                            {
                                              fieldName: 'b_bank_name',
                                              defaultValue:
                                                '$sp_profile.financial.b_bank_name',
                                            },
                                            {
                                              fieldName: 'b_acc_no',
                                              defaultValue:
                                                '$sp_profile.financial.b_acc_no',
                                            },
                                            {
                                              fieldName: 'vat_no',
                                              defaultValue:
                                                '$sp_profile.financial.vat_no',
                                            },
                                            {
                                              fieldName: 'tax_no',
                                              defaultValue:
                                                '$sp_profile.financial.tax_no',
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                            sp_id: '$sp_profile.id',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/field-ops/tasks/edit/directors',
                                          },
                                        ],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks/list-view'],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../areas',
        },
      ],
    },
    // #endregion
    // #region TASKS/EDIT/DIRECTORS SCREEN
    directors: {
      layout: {},
      fetchCalls: [
        {
          key: 'task_details',
          method: 'POST',
          url: '{VITE_SP_SERVER}/api/v1/task_actions/get_task_details',
          slicePath: 'payload',
          successFetchCalls: [
            {
              key: 'directors',
              method: 'POST',
              url: '{VITE_SP_SERVER}/api/v1/spaas_actions/get_directors',
              body: { sp_id: '$object_id' },
              slicePath: 'payload',
            },
          ],
        },
      ],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Enter all director names',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'Changing director names requires approval',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'DirectorsListWithFileView',
          props: {
            directors: '$directors',
            nameField: {
              name: 'director_name',
              placeholder: '',
              label: 'Name',
              fieldAccess: {
                key: 'name',
                path: {
                  edit: 'companydirector',
                  view: 'companydirector',
                  isSpecial: 'companydirector',
                },
              },
            },
            identityField: {
              name: 'director_identity',
              placeholder: '',
              label: 'Identity',
              fieldAccess: {
                key: 'identity',
                path: {
                  edit: 'companydirector',
                  view: 'companydirector',
                  isSpecial: 'companydirector',
                },
              },
            },
            identityFileField: {
              name: 'director_file',
              placeholder: '',
              purpose: 'Identity',
              purposeAsName: true,
              isLandscape: true,
              file_download: '/api/v1/file_actions/director_get_file',
              base_url_name: 'VITE_SP_SERVER',
            },
            disabledWhen: true,
            filesUrl: '/api/v1/file_actions/director_get_files',
            removeDirectorUrl: '/api/v1/spaas_actions/remove_director',
            removeDirectorRedirectUrl: '/field-ops/tasks/edit/directors',
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          // toScreen: '/',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                              You are about to exit this page without saving.
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                              Would you like to save or Clear your changes?
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            'director_name',
                                            'director_identity',
                                            'director_file',
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAsync',
                                        payload: {
                                          calls: [
                                            {
                                              key: 'add_director',
                                              url: '{VITE_SP_SERVER}/api/v1/spaas_actions/add_director',
                                              data: {
                                                name: '$director_name',
                                                identity: '$director_identity',
                                                sp_id: '$sp_profile.id',
                                              },
                                            },
                                            {
                                              key: 'upload_director_file',
                                              url: '{VITE_SP_SERVER}/api/v1/file_actions/director_upload_file',
                                              data: {
                                                file: '$director_file.file',
                                                purpose: 'Identity',
                                                list: 'True',
                                                director_id:
                                                  '{add_director.payload.id}',
                                                sp_id: '$sp_profile.id',
                                              },
                                            },
                                          ],
                                          onFinish: {
                                            type: 'clientAction',
                                            action: 'resetFields',
                                            payload: {
                                              fields: [
                                                'director_name',
                                                'director_identity',
                                                'director_file',
                                              ],
                                            },
                                          },
                                          redirect:
                                            '/field-ops/tasks/list-view',
                                        },
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks/list-view'],
                    },
                    {
                      type: 'clientAction',
                      action: 'clearStore',
                      payload: [
                        'task_details',
                        'sp_profile',
                        'company_documentation',
                        'documentsNotFound',
                        'derivedCompanies',
                        'derivedOperationalArea',
                        'directors',
                        'originalValues',
                      ],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../banking',
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '!$formState.isDirty || !$formState.isValid || !(Object.keys($formState.touchedFields).length > 0)',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: ['director_name', 'director_identity', 'director_file'],
              },
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '!$formState.isDirty || !$formState.isValid || !(Object.keys($formState.touchedFields).length > 0)',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAsync',
              payload: {
                calls: [
                  {
                    key: 'add_director',
                    url: '{VITE_SP_SERVER}/api/v1/spaas_actions/add_director',
                    data: {
                      name: '$director_name',
                      identity: '$director_identity',
                      sp_id: '$sp_profile.id',
                    },
                  },
                  {
                    key: 'upload_director_file',
                    url: '{VITE_SP_SERVER}/api/v1/file_actions/director_upload_file',
                    data: {
                      file: '$director_file.file',
                      purpose: 'Identity',
                      list: 'True',
                      director_id: '{add_director.payload.id}',
                      sp_id: '$sp_profile.id',
                    },
                  },
                ],
                onFinish: {
                  type: 'clientAction',
                  action: 'resetFields',
                  payload: {
                    fields: [
                      'director_name',
                      'director_identity',
                      'director_file',
                    ],
                  },
                },
                redirect: '/field-ops/tasks/edit/directors',
              },
            },
          ],
        },
        { label: 'Next', position: 'right', toScreen: '../contact' },
      ],
    },
    // #endregion
    // #region TASKS/EDIT/BANKING SCREEN
    banking: {
      layout: {},
      fetchCalls: [],
      fragments: [
        {
          component: 'TabsAndOptions',
          props: {
            tabs: [
              {
                name: 'Details',
                path: '../details',
              },
              {
                name: 'Banking',
                path: '../banking',
              },
              {
                name: 'Directors',
                path: '../directors',
              },
              {
                name: 'Contact',
                path: '../contact',
              },
              {
                name: 'Work',
                path: '../work',
              },
              {
                name: 'Areas',
                path: '../areas',
              },
              {
                name: 'Documentation',
                path: '../documentation',
              },
            ],
          },
          isStickyHeader: true,
          layout: {},
        },
        {
          component: 'Text',
          props: {
            textItems: [
              {
                text: 'Company banking details',
                options: {
                  format: 'heading',
                  type: 'page-heading',
                },
              },
              {
                text: 'Changing bank details requires approval',
                options: {
                  format: 'heading',
                  type: 'sub-heading',
                },
                icon: {
                  type: 'alert-diamond',
                  size: 24,
                  strokeWidth: '1px',
                  color: '#FF9800',
                  style: { margin: '0 0 0 4px' },
                },
                iconPosition: 'right',
              },
            ],
          },
          layout: {
            display: 'grid',
            justifyItems: 'center',
            paddingTop: '2rem',
            paddingBottom: '2rem',
          },
        },
        {
          component: 'FormBuilder',
          props: {
            proof_of_bank_account_document:
              "$company_documentation?find:item.purpose === 'Reg Proof of bank account'",
            defaultValues: {
              b_acc_holder: '$sp_profile.financial.b_acc_holder',
              b_branch_name: '$sp_profile.financial.b_branch_name',
              b_acc_type: '$sp_profile.financial.b_acc_type',
              b_branch_code: '$sp_profile.financial.b_branch_code',
              b_bank_name: '$sp_profile.financial.b_bank_name',
              b_acc_no: '$sp_profile.financial.b_acc_no',
              vat_no: '$sp_profile.financial.vat_no',
              tax_no: '$sp_profile.financial.tax_no',
            },
            config: {
              style: {
                display: 'grid',
                gridAutoFlow: 'row',
                rowGap: '2rem',
                columnGap: '1rem',
                justifyItems: 'center',
                alignContent: 'space-around',
                height: 'auto',
                paddingTop: '2rem',
                paddingBottom: '2rem',
                width: '50%',
                minWidth: '712px',
                marginBottom: '6rem',
              },
              controls: [
                {
                  type: 'plain-text',
                  name: 'b_acc_holder',
                  label: 'Bank account holder name',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'b_bank_name',
                  label: 'Bank name',
                  labelProp: 'name',
                  valueProp: 'id',
                  dropdownScroll: true,
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.banks',
                    labelProp: 'name',
                    valueProp: 'id',
                  },
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                  },
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'b_acc_no',
                  label: 'Bank account number',
                  validation: {
                    pattern: {
                      value: validationRegex.bank_account_number.pattern,
                      message: validationRegex.bank_account_number.message,
                    },
                    minLength: {
                      value: 9,
                      message:
                        'Account number is invalid, please confirm banking details',
                    },
                    maxLength: {
                      value: 16,
                      message:
                        'Account number is invalid, please confirm banking details',
                    },
                  },
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'single-select',
                  name: 'b_acc_type',
                  label: 'Account type',
                  labelProp: 'name',
                  valueProp: 'id',
                  dropdownScroll: true,
                  validation: {
                    required: {
                      value: true,
                      message: 'Bank account type is required',
                    },
                  },
                  options: {
                    source: 'store',
                    storeDataPath: 'sp_enums.account_types',
                    labelProp: 'name',
                    valueProp: 'id',
                  },
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'b_branch_name',
                  label: 'Branch name',
                  validation: {
                    required: {
                      value: true,
                      message: 'Branch name is required',
                    },
                    pattern: {
                      value: validationRegex.name.pattern,
                      message: validationRegex.name.message,
                    },
                  },
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'b_branch_code',
                  label: 'Branch code',
                  validation: {
                    required: {
                      value: true,
                      message: 'This field is required',
                    },
                    pattern: {
                      value: validationRegex.branch_code.pattern,
                      message: validationRegex.branch_code.message,
                    },
                  },
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'vat_no',
                  label: 'VAT registration number',
                  validation: {
                    pattern: {
                      value: validationRegex.vat_number.pattern,
                      message: validationRegex.vat_number.message,
                    },
                  },
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'plain-text',
                  name: 'tax_no',
                  label: 'Tax number',
                  validation: {
                    pattern: {
                      value: validationRegex.tax_number.pattern,
                      message: validationRegex.tax_number.message,
                    },
                  },
                  fieldAccessPath: {
                    view: 'financial',
                    edit: 'financial',
                    special: 'financial',
                  },
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
                {
                  type: 'document-card',
                  name: 'proof_of_bank_account_document',
                  documents: '$sp_profile.documents',
                  purpose: 'Reg Proof of bank account',
                  purposeAsName: true,
                  isLandscape: true,
                  enableUpload: false,
                  url: '{VITE_SP_SERVER}/api/v1/file_actions/upload_file',
                  action: '/field-ops/tasks/edit/documentation',
                  list: 'False',
                  css: {
                    wrapper: {
                      height: '4rem',
                      width: '100%',
                    },
                  },
                },
              ],
            },
          },
          layout: { display: 'grid', justifyItems: 'center' },
        },
      ],
      navs: [
        {
          label: 'Back To Field Ops Tool',
          position: 'left',
          // toScreen: '/',
          onClick: [
            {
              type: 'clientAction',
              action: 'conditional',
              payload: {
                condition:
                  '$store?.postData && (Object.keys($store.postData).length > 0) && $formState.isValid',
                actions: {
                  whenTrue: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'Warning',
                                    options: {
                                      format: 'heading',
                                      type: 'page-heading',
                                    },
                                  },
                                  {
                                    text: `
                              You are about to exit this page without saving.
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                      style: {
                                        paddingTop: '2rem',
                                      },
                                    },
                                  },
                                  {
                                    text: `
                              Would you like to save or Clear your changes?
                            `,
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                display: 'grid',
                                gridAutoFlow: 'row',
                                justifyItems: 'center',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: '0 auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Clear Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'resetFields',
                                        payload: {
                                          fields: [
                                            {
                                              fieldName: 'b_acc_holder',
                                              defaultValue:
                                                '$sp_profile.financial.b_acc_holder',
                                            },
                                            {
                                              fieldName: 'b_branch_name',
                                              defaultValue:
                                                '$sp_profile.financial.b_branch_name',
                                            },
                                            {
                                              fieldName: 'b_acc_type',
                                              defaultValue:
                                                '$sp_profile.financial.b_acc_type',
                                            },
                                            {
                                              fieldName: 'b_branch_code',
                                              defaultValue:
                                                '$sp_profile.financial.b_branch_code',
                                            },
                                            {
                                              fieldName: 'b_bank_name',
                                              defaultValue:
                                                '$sp_profile.financial.b_bank_name',
                                            },
                                            {
                                              fieldName: 'b_acc_no',
                                              defaultValue:
                                                '$sp_profile.financial.b_acc_no',
                                            },
                                            {
                                              fieldName: 'vat_no',
                                              defaultValue:
                                                '$sp_profile.financial.vat_no',
                                            },
                                            {
                                              fieldName: 'tax_no',
                                              defaultValue:
                                                '$sp_profile.financial.tax_no',
                                            },
                                          ],
                                        },
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                  {
                                    btnValue: 'Save Changes',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'submitAndFetch',
                                        payload: [
                                          {
                                            postData: '$postData',
                                            sp_id: '$sp_profile.id',
                                          },
                                          {
                                            url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                            headers: {},
                                            bodySlicePath: 'postData',
                                          },
                                          {
                                            method: 'post',
                                            action:
                                              '/field-ops/tasks/list-view',
                                          },
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'clearStore',
                                        payload: [
                                          'task_details',
                                          'sp_profile',
                                          'company_documentation',
                                          'documentsNotFound',
                                          'derivedCompanies',
                                          'derivedOperationalArea',
                                          'directors',
                                          'originalValues',
                                        ],
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                      {
                                        type: 'clientAction',
                                        action: 'navigate',
                                        payload: ['/field-ops/tasks/list-view'],
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                          navs: [],
                        },
                      ],
                    },
                  ],
                  whenFalse: [
                    {
                      type: 'clientAction',
                      action: 'navigate',
                      payload: ['/field-ops/tasks/list-view'],
                    },
                    {
                      type: 'clientAction',
                      action: 'clearStore',
                      payload: [
                        'task_details',
                        'sp_profile',
                        'company_documentation',
                        'documentsNotFound',
                        'derivedCompanies',
                        'derivedOperationalArea',
                        'directors',
                        'originalValues',
                      ],
                    },
                  ],
                },
              },
            },
          ],
        },
        {
          label: 'Previous',
          position: 'left',
          toScreen: '../details',
        },
        {
          label: 'Cancel Changes',
          position: 'center',
          disabledWhen:
            '($store?.postData && !(Object.keys($store.postData).length > 0))',
          onClick: [
            {
              type: 'clientAction',
              action: 'resetFields',
              payload: {
                fields: [
                  {
                    fieldName: 'trading_as',
                    defaultValue: '$sp_profile.details.trading_as',
                  },
                  {
                    fieldName: 'bbeee',
                    defaultValue: '$sp_profile.details.bbeee',
                  },
                  {
                    fieldName: 'co_reg',
                    defaultValue: '$sp_profile.details.co_reg',
                  },
                  {
                    fieldName: 'name',
                    defaultValue: '$sp_profile.details.name',
                  },
                  {
                    fieldName: 'company_type',
                    defaultValue: '$sp_profile.details.company_type',
                  },
                  {
                    fieldName: 'contact_primary',
                    defaultValue: '$sp_profile.address.contact_primary',
                  },
                  {
                    fieldName: 'contact_secondary',
                    defaultValue: '$sp_profile.address.contact_secondary',
                  },
                  {
                    fieldName: 'contact_person',
                    defaultValue: '$sp_profile.address.contact_person',
                  },
                  {
                    fieldName: 'email_receiving',
                    defaultValue: '$sp_profile.address.email_receiving',
                  },
                  {
                    fieldName: 'physical_addr',
                    defaultValue: '$sp_profile.address.physical_addr',
                  },
                  {
                    fieldName: 'physical_city',
                    defaultValue: '$sp_profile.address.physical_city',
                  },
                  {
                    fieldName: 'physical_code',
                    defaultValue: '$sp_profile.address.physical_code',
                  },
                  {
                    fieldName: 'physical_suburb',
                    defaultValue: '$sp_profile.address.physical_suburb',
                  },
                  {
                    fieldName: 'province',
                    defaultValue: '$sp_profile.address.province',
                  },
                  {
                    fieldName: 'postal_code',
                    defaultValue: '$sp_profile.address.postal_code',
                  },
                  {
                    fieldName: 'postal_box',
                    defaultValue: '$sp_profile.address.postal_box',
                  },
                  {
                    fieldName: 'postal_city',
                    defaultValue: '$sp_profile.address.postal_city',
                  },
                  {
                    fieldName: 'skills',
                    defaultValue: '$sp_profile.skills',
                  },
                  {
                    fieldName: 'after_hours',
                    defaultValue: '$sp_profile.after_hours',
                  },
                  {
                    fieldName: 'companies',
                    defaultValue: '$sp_profile.comapanies',
                  },
                  {
                    fieldName: 'accredition',
                    defaultValue:
                      '$sp_profile.additional_identities.accredition',
                  },
                  {
                    fieldName: 'mid',
                    defaultValue: '$sp_profile.additional_identities.mid',
                  },
                  {
                    fieldName: 'operational_area',
                    defaultValue: '$derivedOperationalArea',
                  },
                  {
                    fieldName: 'radius',
                    defaultValue: '$derivedOperationalArea.0.operating_range',
                  },
                  {
                    fieldName: 'jobLocation',
                    defaultValue: '$derivedOperationalArea.0.location',
                  },
                  {
                    fieldName: 'b_acc_holder',
                    defaultValue: '$sp_profile.financial.b_acc_holder',
                  },
                  {
                    fieldName: 'b_branch_name',
                    defaultValue: '$sp_profile.financial.b_branch_name',
                  },
                  {
                    fieldName: 'b_acc_type',
                    defaultValue: '$sp_profile.financial.b_acc_type',
                  },
                  {
                    fieldName: 'b_branch_code',
                    defaultValue: '$sp_profile.financial.b_branch_code',
                  },
                  {
                    fieldName: 'b_bank_name',
                    defaultValue: '$sp_profile.financial.b_bank_name',
                  },
                  {
                    fieldName: 'b_acc_no',
                    defaultValue: '$sp_profile.financial.b_acc_no',
                  },
                  {
                    fieldName: 'vat_no',
                    defaultValue: '$sp_profile.financial.vat_no',
                  },
                  {
                    fieldName: 'tax_no',
                    defaultValue: '$sp_profile.financial.tax_no',
                  },
                ],
              },
            },
          ],
        },
        {
          label: 'Save Changes',
          position: 'center',
          disabledWhen:
            '!$store?.postData || !(Object.keys($store.postData).length > 0) || (Object.keys($formState.errors).length > 0)|| !$formState.isDirty',
          onClick: [
            {
              type: 'clientAction',
              action: 'submitAndFetch',
              payload: [
                {
                  postData: '$postData',
                  sp_id: '$sp_profile.id',
                },
                {
                  url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                  headers: {},
                  bodySlicePath: 'postData',
                },
                {
                  method: 'post',
                  action: '/field-ops/tasks/edit/directors',
                },
              ],
            },
          ],
        },
        { label: 'Next', position: 'right', toScreen: '../directors' },
      ],
    },
    // #endregion
  },
  /*
   * SECTION: TASKS/EDIT STATE FORM ORIGINAL VALUES
   * tracking of form values for composing postData
   */
  // #region OriginalValues configuration
  formOriginalValues: {
    // Orignals for diffing to find changed fields (from clientDataObject to store)
    'sp_profile.details.trading_as': 'trading_as',
    'sp_profile.details.bbeee': 'bbeee',
    'sp_profile.details.name': 'name',
    'sp_profile.details.co_reg': 'co_reg',
    'sp_profile.details.company_type': 'company_type',

    'sp_profile.financial.vat_registered': 'vat_registered',
    'sp_profile.financial.vat_no': 'vat_no',
    'sp_profile.financial.tax_no': 'tax_no',
    'sp_profile.financial.b_acc_holder': 'b_acc_holder',
    'sp_profile.financial.b_acc_type': 'b_acc_type',
    'sp_profile.financial.b_acc_no': 'b_acc_no',
    'sp_profile.financial.b_bank_name': 'b_bank_name',
    'sp_profile.financial.b_branch_name': 'b_branch_name',
    'sp_profile.financial.b_branch_code': 'b_branch_code',

    'sp_profile.address.contact_primary': 'contact_primary',
    'sp_profile.address.contact_secondary': 'contact_secondary',
    'sp_profile.address.contact_person': 'contact_person',
    'sp_profile.address.email_receiving': 'email_receiving',
    'sp_profile.address.physical_addr': 'physical_addr',
    'sp_profile.address.physical_city': 'physical_city',
    'sp_profile.address.physical_suburb': 'physical_suburb',
    'sp_profile.address.physical_code': 'physical_code',
    'sp_profile.address.postal_box': 'postal_box',
    'sp_profile.address.postal_city': 'postal_city',
    'sp_profile.address.postal_code': 'postal_code',
    'sp_profile.address.province': 'province',

    'sp_profile.skills': 'skills',
    // 'sp_profile.companies': 'companies',
    derivedCompanies: 'companies',
    'sp_profile.after_hours': 'after_hours',

    derivedOperationalArea: 'operational_area',

    'sp_profile.additional_identities.accredition': 'accredition',
    'sp_profile.additional_identities.mid': 'mid',
    'sp_profile.onboarding_state': 'onboarding_state',
  },
  // #endregion
  // #region FormTransformMapper configuration
  formTransformMapper: {
    // Actual mapper to get final object shaped for server
    trading_as: 'details.trading_as',
    bbeee: 'details.bbeee',
    name: 'details.name',
    co_reg: 'details.co_reg',
    company_type: 'details.company_type',

    vat_registered: 'financial.vat_registered',
    vat_no: 'financial.vat_no',
    tax_no: 'financial.tax_no',
    b_acc_holder: 'financial.b_acc_holder',
    b_acc_type: 'financial.b_acc_type',
    b_acc_no: 'financial.b_acc_no',
    b_bank_name: 'financial.b_bank_name',
    b_branch_name: 'financial.b_branch_name',
    b_branch_code: 'financial.b_branch_code',

    contact_primary: 'address.contact_primary',
    contact_secondary: 'address.contact_secondary',
    contact_person: 'address.contact_person',
    email_receiving: 'address.email_receiving',
    physical_addr: 'address.physical_addr',
    physical_city: 'address.physical_city',
    physical_suburb: 'address.physical_suburb',
    physical_code: 'address.physical_code',
    postal_box: 'address.postal_box',
    postal_city: 'address.postal_city',
    postal_code: 'address.postal_code',
    province: 'address.province',

    skills: 'skills',
    companies: 'companies',
    after_hours: 'after_hours',

    operational_area: 'operational_area',

    mid: 'additional_identities.mid',
    accredition: 'additional_identities.accredition',
    onboarding_state: 'onboarding_state',
  },
  // #endregion
  /*
   * SECTION: TASKS/EDIT ACTION PANELS
   * Add all action panel item configurations here
   */
  // #region TASKS/EDIT ACTION PANELS
  actionPanels: [
    // #region APPROVALS PANEL
    {
      icon: 'file-check-02',
      title: 'Approvals',
      layout: {
        display: 'grid',
        gridAutoFlow: 'row',
        gap: '1rem',
        position: 'relative',
        paddingBottom: '4rem',
      },
      onEnter: [],
      onLeave: [],
      scroll: false,
      fragments: [
        {
          component: 'FormBuilder',
          layout: {},
          props: {
            config: {
              style: {
                padding: '0.5rem',
              },
              controls: [
                {
                  type: 'checklist',
                  title: 'Check List',
                  items: [
                    {
                      name: 'company_registration_number_verified_valid',
                      label: 'confirm registration number',
                    },
                    {
                      name: 'company_bbbeee_level_verified_valid',
                      label: 'confirm bbbeee level',
                    },
                    {
                      name: 'company_vat_number_verified_valid',
                      label: 'confirm vat number',
                    },
                    {
                      name: 'company_tax_number_verified_valid',
                      label: 'confirm tax number',
                    },
                    {
                      name: 'company_banking_details_verified_valid',
                      label: 'confirm banking details',
                    },
                    {
                      name: 'director_id_documents_verified_valid',
                      label: 'confirm director ids',
                    },
                    // {
                    //   name: 'sil_mid_accredition_verified_valid',
                    //   label: 'check sil mid/multichoice accredition',
                    // },
                    {
                      name: 'company_documentation_verified_valid',
                      label: 'verify all added documents',
                    },
                  ],
                  submitOnChange: true,
                  onInvalid: [
                    {
                      type: 'clientAction',
                      action: 'triggerModal',
                      payload: [
                        {
                          display: true,
                          type: 'warning',
                          heading: 'Please note',
                          headingType: 'page-heading',
                          layout: {},
                          onEnter: [],
                          onLeave: [],
                          fragments: [
                            {
                              component: 'Text',
                              props: {
                                textItems: [
                                  {
                                    text: 'You will need to contact the Service Provider (SP) to\nresolve any issues regarding their registration',
                                    options: {
                                      format: 'heading',
                                      type: 'sub-heading',
                                    },
                                  },
                                ],
                              },
                              layout: {
                                justifyItems: 'center',
                                display: 'grid',
                              },
                            },
                            {
                              component: 'ButtonRow',
                              layout: {
                                width: 'fit-content',
                                margin: 'auto',
                              },
                              props: {
                                buttons: [
                                  {
                                    btnValue: 'Okay',
                                    onClick: [
                                      {
                                        type: 'clientAction',
                                        action: 'closeModal',
                                      },
                                    ],
                                  },
                                ],
                              },
                            },
                          ],
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          },
        },
        {
          component: 'FormBuilder',
          layout: {
            marginBottom: '6rem',
            paddingBottom: '4rem',
          },
          props: {
            config: {
              style: {
                padding: '0.5rem',
              },
              controls: [
                {
                  type: 'tenant-approval',
                  title: 'Tenant Approval',
                  tenantsStorePath: 'sp_profile.companies',
                  submitOnChange: true,
                },
              ],
            },
          },
        },
        {
          component: 'ButtonRow',
          layout: {
            position: 'absolute',
            bottom: '0',
            margin: '0.5rem auto 0',
            left: '50%',
            transform: 'translateX(-50%)',
            width: '326px',
            zIndex: 2,
          },
          props: {
            background: 'paginationStyle',
            buttons: [
              // #region HARD REJECTION BUTTON
              {
                btnValue: 'Reject',
                actiontype: 'alternative',
                disabledWhen: `(!$form.company_registration_number_verified_valid &&
                !$form.company_bbbeee_level_verified_valid &&
                !$form.company_vat_number_verified_valid &&
                !$form.company_tax_number_verified_valid &&
                !$form.company_banking_details_verified_valid &&
                !$form.director_id_documents_verified_valid &&
                !$form.company_documentation_verified_valid) ||
                ($form.company_registration_number_verified_valid==='unchecked' ||
                $form.company_bbbeee_level_verified_valid==='unchecked' ||
                $form.company_vat_number_verified_valid==='unchecked' ||
                $form.company_tax_number_verified_valid==='unchecked' ||
                $form.company_banking_details_verified_valid==='unchecked' ||
                $form.director_id_documents_verified_valid==='unchecked' ||
                $form.company_documentation_verified_valid==='unchecked')`,
                // !$form.sil_mid_accredition_verified_valid &&
                // $form.sil_mid_accredition_verified_valid==='unchecked' ||
                onClick: [
                  {
                    type: 'clientAction',
                    action: 'triggerModal',
                    payload: [
                      {
                        display: true,
                        type: 'warning',
                        layout: {
                          height: '75%',
                        },
                        onEnter: [],
                        onLeave: [],
                        fragments: [
                          {
                            component: 'Text',
                            props: {
                              textItems: [
                                {
                                  text: 'Please note',
                                  options: {
                                    format: 'heading',
                                    type: 'page-heading',
                                  },
                                },
                                {
                                  text: `
                          You are about to reject this SP and they will need to
                        `,
                                  options: {
                                    format: 'heading',
                                    type: 'sub-heading',
                                    style: {
                                      paddingTop: '1rem',
                                      textAlign: 'center',
                                    },
                                  },
                                },
                                {
                                  text: `
                          reregister to be considered again. Are you sure you
                        `,
                                  options: {
                                    format: 'heading',
                                    type: 'sub-heading',
                                    style: {
                                      textAlign: 'center',
                                    },
                                  },
                                },
                                {
                                  text: `
                          would like to proceed to reject them?
                        `,
                                  options: {
                                    format: 'heading',
                                    type: 'sub-heading',
                                    style: {
                                      textAlign: 'center',
                                    },
                                  },
                                },
                              ],
                            },
                            layout: {
                              display: 'grid',
                              gridAutoFlow: 'row',
                              justifyItems: 'center',
                            },
                          },
                          {
                            component: 'ButtonRow',
                            layout: {
                              width: 'fit-content',
                              margin: '0 auto',
                            },
                            props: {
                              buttons: [
                                {
                                  btnValue: 'No, cancel',
                                  onClick: [
                                    {
                                      type: 'clientAction',
                                      action: 'closeModal',
                                    },
                                  ],
                                },
                                {
                                  btnValue: 'Yes, reject SP',
                                  onClick: [
                                    {
                                      type: 'clientAction',
                                      action: 'submitAndNavigate',
                                      payload: [
                                        {
                                          sp_id: '$task_details.object_id',
                                          reason:
                                            'Validation requirements not met',
                                          task_id: '$task_details.id',
                                          full: true,
                                        },
                                        {
                                          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/reject_sp_registration',
                                          headers: {},
                                          redirect: '/field-ops/tasks',
                                        },
                                        {
                                          method: 'post',
                                          action:
                                            '/field-ops/tasks/edit/details',
                                        },
                                      ],
                                    },
                                    {
                                      type: 'clientAction',
                                      action: 'closeModal',
                                    },
                                    {
                                      type: 'clientAction',
                                      action: 'clearStore',
                                      payload: [
                                        'task_details',
                                        'sp_profile',
                                        'company_documentation',
                                      ],
                                    },
                                  ],
                                },
                              ],
                            },
                          },
                        ],
                        navs: [],
                      },
                    ],
                  },
                ],
              },
              // #endregion
              // #region SOFT REJECTION BUTTON
              {
                // btnValue: `#{ifThen(sp_profile.onboarding_state, 'sp to fix', 'enquire')}`,
                btnValue: `sp to fix`,
                actiontype: 'alternative',
                onClick: [
                  {
                    type: 'clientAction',
                    action: 'triggerModal',
                    payload: [
                      {
                        display: true,
                        type: 'warning',
                        layout: {
                          height: '75%',
                        },
                        onEnter: [],
                        onLeave: [],
                        fragments: [
                          {
                            component: 'Text',
                            props: {
                              textItems: [
                                {
                                  text: 'Return to SP for fix',
                                  options: {
                                    format: 'heading',
                                    type: 'page-heading',
                                  },
                                },
                                {
                                  text: `
                          Preferably complete all checks before returning to the
                        `,
                                  options: {
                                    format: 'heading',
                                    type: 'sub-heading',
                                    style: {
                                      paddingTop: '1rem',
                                      textAlign: 'center',
                                    },
                                  },
                                },
                                {
                                  text: `
                          SP. Then they can make all amendments at once.
                        `,
                                  options: {
                                    format: 'heading',
                                    type: 'sub-heading',
                                    style: {
                                      textAlign: 'center',
                                    },
                                  },
                                },
                              ],
                            },
                            layout: {
                              display: 'grid',
                              gridAutoFlow: 'row',
                              justifyItems: 'center',
                            },
                          },
                          {
                            component: 'FormBuilder',
                            layout: {
                              display: 'grid',
                              justifyItems: 'center',
                              marginTop: '2rem',
                            },
                            props: {
                              config: {
                                style: { width: '100%' },
                                controls: [
                                  {
                                    type: 'textarea',
                                    name: 'field_ops_feedback',
                                    label: 'What would you like the SP to fix?',
                                    rows: 10,
                                    validation: {
                                      required: {
                                        value: true,
                                        message: 'This field is required',
                                      },
                                    },
                                  },
                                ],
                              },
                            },
                          },
                          {
                            component: 'ButtonRow',
                            layout: {
                              width: 'fit-content',
                              margin: '0 auto',
                            },
                            props: {
                              buttons: [
                                {
                                  btnValue: 'Cancel',
                                  onClick: [
                                    {
                                      type: 'clientAction',
                                      action: 'resetFields',
                                      payload: {
                                        fields: ['field_ops_feedback'],
                                      },
                                    },
                                    {
                                      type: 'clientAction',
                                      action: 'closeModal',
                                    },
                                  ],
                                },
                                {
                                  btnValue: 'Send to SP',
                                  onClick: [
                                    {
                                      type: 'clientAction',
                                      action: 'triggerFetchCall',
                                      payload: [
                                        {
                                          key: 'sp_profile',
                                          method: 'POST',
                                          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/update_sp',
                                          body: {
                                            sp_id: '#{task_details.object_id}',
                                            details: {
                                              additional: {
                                                $: '...{sp_profile.details.additional}',
                                                field_ops_feedback:
                                                  '#{formDataRaw.field_ops_feedback}',
                                              },
                                            },
                                          },
                                          successFetchCalls: [
                                            {
                                              key: 'sendToSp',
                                              method: 'POST',
                                              url: '{VITE_SP_SERVER}/api/v1/spaas_actions/reject_sp_registration',
                                              body: {
                                                sp_id:
                                                  '#{task_details.object_id}',
                                                reason:
                                                  'Requested amendments to registration',
                                                task_id: '#{task_details.id}',
                                                full: false,
                                              },
                                            },
                                          ],
                                        },
                                      ],
                                      async: true,
                                      asyncLoadStart: true,
                                    },
                                    {
                                      type: 'clientAction',
                                      action: 'closeModal',
                                      async: true,
                                    },
                                    {
                                      type: 'clientAction',
                                      action: 'navigate',
                                      payload: ['/field-ops/tasks/list-view'],
                                      async: true,
                                      asyncLoadEnd: true,
                                    },
                                  ],
                                },
                              ],
                            },
                          },
                        ],
                        navs: [],
                      },
                    ],
                  },
                ],
              },
              // #endregion
              // #region APPROVAL BUTTON
              {
                btnValue: 'Accept',
                color: 'primary',
                disabledWhen: `(!$form.company_registration_number_verified_valid ||
                !$form.company_bbbeee_level_verified_valid ||
                !$form.company_vat_number_verified_valid ||
                !$form.company_tax_number_verified_valid ||
                !$form.company_banking_details_verified_valid ||
                !$form.director_id_documents_verified_valid ||
                !$form.company_documentation_verified_valid) ||
                ($form.company_registration_number_verified_valid==='unchecked' ||
                $form.company_bbbeee_level_verified_valid==='unchecked' ||
                $form.company_vat_number_verified_valid==='unchecked' ||
                $form.company_tax_number_verified_valid==='unchecked' ||
                $form.company_banking_details_verified_valid==='unchecked' ||
                $form.director_id_documents_verified_valid==='unchecked' ||
                $form.company_documentation_verified_valid==='unchecked') ||
                ($form.company_registration_number_verified_valid==='invalid' ||
                $form.company_bbbeee_level_verified_valid==='invalid' ||
                $form.company_vat_number_verified_valid==='invalid' ||
                $form.company_tax_number_verified_valid==='invalid' ||
                $form.company_banking_details_verified_valid==='invalid' ||
                $form.director_id_documents_verified_valid==='invalid' ||
                $form.company_documentation_verified_valid==='invalid') ||
                !$form.tenant_subscriptions_evaluated`,
                // !$form.sil_mid_accredition_verified_valid ||
                // $form.sil_mid_accredition_verified_valid==='unchecked' ||
                // $form.sil_mid_accredition_verified_valid==='invalid' ||
                // ($store.documentsNotFound && $store.documentsNotFound.length > 0)`,
                actiontype: 'preferred',
                onClick: [
                  {
                    type: 'clientAction',
                    action: 'triggerModal',
                    payload: [
                      {
                        display: true,
                        type: 'warning',
                        layout: {
                          height: '75%',
                        },
                        onEnter: [],
                        onLeave: [],
                        fragments: [
                          {
                            component: 'Text',
                            props: {
                              textItems: [
                                {
                                  text: 'Please note',
                                  options: {
                                    format: 'heading',
                                    type: 'page-heading',
                                  },
                                },
                                {
                                  text: `
                          You are about to approve this Service Provider. Once
                        `,
                                  options: {
                                    format: 'heading',
                                    type: 'sub-heading',
                                    style: {
                                      paddingTop: '1rem',
                                      textAlign: 'center',
                                    },
                                  },
                                },
                                {
                                  text: `
                          approved, they must be contacted to schedule
                        `,
                                  options: {
                                    format: 'heading',
                                    type: 'sub-heading',
                                    style: {
                                      textAlign: 'center',
                                    },
                                  },
                                },
                                {
                                  text: `
                          training.
                        `,
                                  options: {
                                    format: 'heading',
                                    type: 'sub-heading',
                                    style: {
                                      textAlign: 'center',
                                    },
                                  },
                                },
                                {
                                  text: `
                          Are you sure you want to proceed?
                        `,
                                  options: {
                                    format: 'heading',
                                    type: 'sub-heading',
                                    style: {
                                      textAlign: 'center',
                                    },
                                  },
                                },
                              ],
                            },
                            layout: {
                              display: 'grid',
                              gridAutoFlow: 'row',
                              justifyItems: 'center',
                            },
                          },
                          {
                            component: 'ButtonRow',
                            layout: {
                              width: 'fit-content',
                              margin: '0 auto',
                            },
                            props: {
                              buttons: [
                                {
                                  btnValue: 'No, cancel',
                                  onClick: [
                                    {
                                      type: 'clientAction',
                                      action: 'resetFields',
                                      payload: {
                                        fields: ['field_ops_feedback'],
                                      },
                                    },
                                    {
                                      type: 'clientAction',
                                      action: 'closeModal',
                                    },
                                  ],
                                },
                                {
                                  btnValue: 'Yes, approve SP',
                                  onClick: [
                                    {
                                      type: 'clientAction',
                                      action: 'submitAndNavigate',
                                      payload: [
                                        {
                                          sp_id: '$task_details.object_id',
                                          task_id: '$task_details.id',
                                        },
                                        {
                                          url: '{VITE_SP_SERVER}/api/v1/spaas_actions/approve_sp_registration',
                                          headers: {},
                                          redirect:
                                            '/field-ops/tasks/list-view',
                                        },
                                        {
                                          method: 'post',
                                          action:
                                            '/field-ops/tasks/edit/details',
                                        },
                                      ],
                                    },
                                    {
                                      type: 'clientAction',
                                      action: 'closeModal',
                                    },
                                    {
                                      type: 'clientAction',
                                      action: 'clearStore',
                                      payload: [
                                        'task_details',
                                        'sp_profile',
                                        'company_documentation',
                                      ],
                                    },
                                  ],
                                },
                              ],
                            },
                          },
                        ],
                        navs: [],
                      },
                    ],
                  },
                ],
              },
              // #endregion
            ],
            btnFixedWidth: false,
          },
        },
      ],
      actionLevel: 'topControls',
    },
    // #endregion
    // #region SCRATCH PAD
    {
      icon: 'clipboard',
      title: 'Scratch Pad', //?actionPanel=Messages--bell-02
      // fetchCalls: [],
      layout: {},
      onEnter: [],
      onLeave: [],
      fragments: [
        {
          component: 'ScratchPadView',
          layout: { marginLeft: '10px', marginRight: '10px' },
          props: {
            titlePlaceholder: 'Heading',
            icon: 'trash-01',
            iconHandler: (data: { heading: string; body: string }) =>
              console.log(
                'got data: Heading - ' + data.heading + ' Body - ' + data.body
              ),
            placeHolder: 'Text here...',
          },
        },
      ],
      actionLevel: 'bottomControls',
    },
    // #endregion
  ],
  // #endregion
  activeActionPanelView: 'Approvals',
} satisfies StateConfig;
// #endregion
