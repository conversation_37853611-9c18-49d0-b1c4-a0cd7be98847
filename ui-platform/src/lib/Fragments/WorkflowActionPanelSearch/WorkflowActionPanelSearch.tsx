import styled from "styled-components";
import { SearchInput } from "../../Components/Inputs/SearchInput/SearchInput";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Switch } from "../../Components/Inputs/Switch/Switch";

const PanelContainer = styled.div`
  height: 100%;
  width: 400px;
  // background: #3d4143;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  padding-top: 32px;
  align-items: left;
`;

// const Title = styled.h2`
//   color: #fff;
//   font-size: 24px;
//   font-weight: 600;
//   // text-align: center;
//   margin: 0 0 12px 0;
// `;

// const TitleUnderlineContainer = styled.div`
//   display: flex;
//   flex-direction: column;
//   // align-items: center;
//   // width: 100%;
//   align-items: center;
//   margin-bottom: 0;
//   max-width: 320px;
// `;

// const Underline = styled.div`
//   width: 270px;
//   height: 2px;
//   background: #2e8bb8;
//   margin: 0 0 24px 0;
//   border-radius: 2px;
// `;

const SearchRow = styled.div`
  display: flex;
  width: 90%;
  max-width: 320px;
  justify-content: center;
  margin-bottom: 16px;
`;

const WorkflowSearch = styled(SearchInput)`
  width: 100% !important;
  height: 40px !important;
  box-sizing: border-box;
  border-radius: 6px !important;
  // background: #45494b !important;
  border: 1px solid #6c757d !important;
  color: #fff !important;
  font-size: 16px !important;
  padding-right: 36px !important;
`;

const SwitchRow = styled.div`
  display: flex;
  // align-items: center;
  margin-top: 8px;
  margin-bottom: 0;
  width: 90%;
  max-width: 320px;
  margin-left: 2.6rem;
`;

const SwitchLabel = styled.span`
  color: #bfc3c5;
  font-size: 15px;
  margin-left: 12px;
  user-select: none;
  padding-top: 6px;
`;

export function WorkflowActionPanelSearch({ searchUrl, items, setSearchResults, token, tokenPrefix }: { searchUrl: string, items: any[], setSearchResults: any, token: string, tokenPrefix: string }) {
      const [searchTerm, setSearchTerm] = useState('');
        const [isSearching, setIsSearching] = useState(false);
        const latestSearchId = useRef<number>(0);
    const abortControllerRef = useRef<AbortController | null>(null);
    const [isClosedClaim, setIsClosedClaim] = useState(false);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const term = event.target.value;
    setSearchTerm(term);
    // updateUrl(filterConditions, term);
    // No longer triggering search on change, only updating the search term
  };

    // Direct search function without debouncing
    const performSearch = useCallback(async (term: string) => {
      const searchId = Date.now(); // Generate unique ID for this search request
      latestSearchId.current = searchId;
      
      if (!term || !searchUrl) {
        setIsSearching(false);
        return;
      }
  
      setIsSearching(true);
      try {
        // Cancel any ongoing search request
        if (abortControllerRef.current) {
          abortControllerRef.current.abort();
        }
        
        // Create a new abort controller for this request
        const abortController = new AbortController();
        abortControllerRef.current = abortController;
        
        const response = await fetch(`${searchUrl}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            authorization: `${tokenPrefix} ${token}`,
          },
          body: JSON.stringify({
            search: term,
            active: false,
            searchId, // Include searchId to track request
          }),
          signal: abortController.signal,
        });
  
        if (!response.ok) throw new Error('Search request failed');
        const data = await response.json();
        const payload = data.payload || [];
        
        // Only update if this is still the latest search
        if (searchId === latestSearchId.current) {
          setSearchResults(payload);
        }
      } catch (error) {
        if ((error as any)?.name === 'AbortError') return; // build type error fix
        console.error('Search error:', error);
        setSearchResults(items);
      } finally {
        setIsSearching(false);
      }
    }, [searchUrl]);
  
  // Handle search submission when the icon is clicked
  const handleSearchSubmit = () => {
    performSearch(searchTerm);
  };

  const handleIncludeClosedClaims = (value: boolean) => {
    setIsClosedClaim(value);
    // performSearch(searchTerm);
  };
    // Cleanup function for search requests
    useEffect(() => {
        return () => {
          if (abortControllerRef.current) {
            abortControllerRef.current.abort();
          }
        };
      }, []);

    return (
        <PanelContainer>
          {/* <TitleUnderlineContainer>
            <Title>Search</Title>
            <Underline />
          </TitleUnderlineContainer> */}
          <SearchRow>
            <WorkflowSearch
              placeholder="Search"
              value={searchTerm}
              onChange={handleSearchChange}
              onSubmit={handleSearchSubmit}
              isLoading={isSearching}
            />
          </SearchRow>
          <SwitchRow>
            <Switch
              enabled="On"
              disabled="Off"
              name="isClosedClaim"
              isOn={isClosedClaim}
              onChange={handleIncludeClosedClaims}
            />
            <SwitchLabel>Include closed claims</SwitchLabel>
          </SwitchRow>
        </PanelContainer>
    );
}