import React, { useState, useEffect, useRef } from "react";
import styled from "styled-components";
import { FilterCondition } from "../../Engine/models/filter-condition";
import { SearchInput } from "../../Components/Inputs/SearchInput/SearchInput";
import { PlainTextInput } from "../../Components/Inputs/PlainTextInput/PlainTextInput";
import { Icon } from "../../Components/Icons";

const StyledWorkflowActionPanelFilters = styled.div`
  // background: #444;
  margin: 0;
  padding: 24px 16px 24px 16px;
  width: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 6px;
`;

const FilterHeader = styled.div`
  font-size: 24px;
  font-weight: 700;
  color: #fff;
  margin-top: 24px;
  margin-bottom: 8px;
  text-align: center;
  padding-bottom: 0;
`;

const Underline = styled.div`
  width: 350px;
  height: 2px;
  background: #2e8bb8;
  margin: 8px auto 24px auto;
  border-radius: 2px;
`;

const FilterContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  padding: 0 0 16px 0;
`;

const FilterItem = styled.div`
  border: 1px solid #bfc6cc;
  border-radius: 6px;
  padding: 10px 12px;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.2s;
  text-align: left;
  margin-bottom: 0;
  &:hover {
    background-color: #4a5568;
  }
`;

const SelectedFiltersSection = styled.div`
  margin-top: 0;
  padding: 16px 0 0 0;
  border-radius: 4px;
`;

const SelectedFiltersHeader = styled.div`
  font-size: 20px;
  color: #fff;
  font-weight: 500;
  margin-bottom: 16px;
  text-align: center;
`;

const SelectedFilterItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #2D3748;
`;

const FilterName = styled.div`
  font-size: 16px;
  color: #ecc94b;
  font-weight: 500;
`;

const FilterValue = styled.div`
  font-size: 16px;
  color: #fff;
  margin-top: 0;
`;

const CloseButton = styled.div`
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
`;

const AddFilterButton = styled.button`
  background-color: transparent;
  color: #2e8bb8;
  border: 1px solid #2e8bb8;
  border-radius: 999px;
  padding: 8px 24px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  margin-top: 16px;
  width: auto;
  align-self: center;
  transition: background 0.2s;
  &:hover {
    background-color: rgba(46, 139, 184, 0.1);
  }
`;

const FilterValueForm = styled.div`
  padding: 0;
  border-radius: 4px;
  margin-top: 0;
`;

const FormHeader = styled.div`
  font-size: 20px;
  color: #fff;
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
`;

const FormInputsContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 16px;
`;

const ContinueButton = styled.button`
  background-color: #4299e1;
  color: #fff;
  border: none;
  border-radius: 4px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  width: 100%;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background-color: #3182ce;
  }
`;

const StateSelectionContainer = styled.div`
  padding: 0;
  border-radius: 4px;
  margin-top: 0;
`;

const StateHeader = styled.div`
  font-size: 20px;
  color: #fff;
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: relative;
`;

const StateList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  margin-top: 16px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #2D3748;
  }
  
  &::-webkit-scrollbar-thumb {
    background-color: #4A5568;
    border-radius: 6px;
  }
`;

const StateItem = styled.div`
  padding: 12px 16px;
  border-radius: 4px;
  color: #fff;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &:hover {
    background-color: #4A5568;
  }
`;

const StateCount = styled.span`
  color: #A0AEC0;
  font-size: 14px;
`;

const StyledSearchInputWrapper = styled.div`
  width: 100%;
  margin-bottom: 12px;
  input {
    width: 100%;
    background: #444;
    border: 1px solid #bfc6cc;
    border-radius: 6px;
    color: #fff;
    font-size: 16px;
    padding: 10px 36px 10px 12px;
    box-sizing: border-box;
  }
  input:focus {
    outline: none;
    box-shadow: none;
    border-color: #bfc6cc;
  }
`;

export interface FilterOption {
  id: string;
  name: string;
  type: 'range' | 'select' | 'text';
  options?: Array<{
    id: string;
    name: string;
    count?: number;
  }>;
}

export interface AppliedFilter {
  id: string;
  name: string;
  value: string | number | Array<string | number>;
  filterCondition: FilterCondition;
}

export interface WorkflowActionPanelFiltersProps {
  filters: FilterOption[];
  label?: string;
  onFilterApply?: (filter: AppliedFilter) => void;
  onFilterRemove?: (filterId: string) => void;
  _onNotify?: Function;
}

export function WorkflowActionPanelFilters({
  filters = [],
  label = "Filter Workflow",
  onFilterApply,
  onFilterRemove,
  _onNotify
}: WorkflowActionPanelFiltersProps) {
  const [appliedFilters, setAppliedFilters] = useState<AppliedFilter[]>([]);
  const [selectedFilter, setSelectedFilter] = useState<FilterOption | null>(null);
  const [filterView, setFilterView] = useState<'main' | 'form' | 'select'>('main');
  
  // For range type filters
  const [minValue, setMinValue] = useState<string>('');
  const [maxValue, setMaxValue] = useState<string>('');
  
  // For select type filters
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filteredOptions, setFilteredOptions] = useState<any[]>([]);
  
  useEffect(() => {
    if (selectedFilter?.options) {
      setFilteredOptions(selectedFilter.options);
    }
  }, [selectedFilter]);
  
  const handleFilterSelect = (filter: FilterOption) => {
    setSelectedFilter(filter);
    setShowAvailableFilters(false); // Hide available filters when a filter is selected
    
    if (filter.type === 'range') {
      setFilterView('form');
    } else if (filter.type === 'select') {
      setFilterView('select');
      setFilteredOptions(filter.options || []);
    } else {
      setFilterView('form');
    }
  };
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value;
    setSearchTerm(term);
    
    if (selectedFilter?.options) {
      const filtered = selectedFilter.options.filter(option => 
        option.name.toLowerCase().includes(term.toLowerCase())
      );
      setFilteredOptions(filtered);
    }
  };
  
  const handleContinue = () => {
    if (!selectedFilter) return;
    
    if (selectedFilter.type === 'range') {
      const min = parseFloat(minValue);
      const max = parseFloat(maxValue);
      
      if (isNaN(min) || isNaN(max)) return;
      
      const newFilter: AppliedFilter = {
        id: selectedFilter.id,
        name: selectedFilter.name,
        value: [min, max],
        filterCondition: {
          name: selectedFilter.id,
          key: selectedFilter.id,
          value: min,
          secondValue: max,
          operator: 'between'
        }
      };
      
      const updatedFilters = [...appliedFilters.filter(f => f.id !== selectedFilter.id), newFilter];
      setAppliedFilters(updatedFilters);
      
      if (onFilterApply) {
        onFilterApply(newFilter);
      }
      
      if (_onNotify) {
        _onNotify({
          appliedFilters: updatedFilters
        });
      }
      
      // Reset form
      setMinValue('');
      setMaxValue('');
      setFilterView('main');
      setSelectedFilter(null);
    }
  };
  
  const handleStateSelect = (option: any) => {
    if (!selectedFilter) return;
    
    const newFilter: AppliedFilter = {
      id: selectedFilter.id,
      name: selectedFilter.name,
      value: option.name,
      filterCondition: {
        name: selectedFilter.id,
        key: selectedFilter.id,
        value: option.id,
        operator: 'equals'
      }
    };
    
    const updatedFilters = [...appliedFilters.filter(f => f.id !== selectedFilter.id), newFilter];
    setAppliedFilters(updatedFilters);
    
    if (onFilterApply) {
      onFilterApply(newFilter);
    }
    
    if (_onNotify) {
      _onNotify({
        appliedFilters: updatedFilters
      });
    }
    
    // Reset
    setSearchTerm('');
    setFilterView('main');
    setSelectedFilter(null);
  };
  
  const handleRemoveFilter = (filterId: string) => {
    const updatedFilters = appliedFilters.filter(filter => filter.id !== filterId);
    setAppliedFilters(updatedFilters);
    
    if (onFilterRemove) {
      onFilterRemove(filterId);
    }
    
    if (_onNotify) {
      _onNotify({
        appliedFilters: updatedFilters
      });
    }
  };
  
  const handleBackToMain = () => {
    setFilterView('main');
    setSelectedFilter(null);
    setMinValue('');
    setMaxValue('');
    setSearchTerm('');
  };

  // Track whether we're showing available filters or just the applied filters
  const [showAvailableFilters, setShowAvailableFilters] = useState<boolean>(false);
  
  const renderMainView = () => (
    <>
      {appliedFilters.length > 0 && (
        <SelectedFiltersSection>
          <SelectedFiltersHeader>Selected Filters</SelectedFiltersHeader>
          <Underline />
          {appliedFilters.map(filter => (
            <SelectedFilterItem key={filter.id}>
              <div>
                <FilterName>{filter.name}</FilterName>
                <FilterValue>
                  {Array.isArray(filter.value) 
                    ? filter.value.join(' - ')
                    : filter.value}
                </FilterValue>
              </div>
              <CloseButton onClick={() => handleRemoveFilter(filter.id)}>
                <Icon type="close" color="#fff" />
              </CloseButton>
            </SelectedFilterItem>
          ))}
          
          {/* Show available filters when the Add Filter button has been clicked */}
          {showAvailableFilters && filters.length > 0 && (
            <FilterContainer>
              {filters
                .filter(filter => !appliedFilters.some(af => af.id === filter.id))
                .map(filter => (
                  <FilterItem 
                    key={filter.id} 
                    onClick={() => handleFilterSelect(filter)}
                  >
                    {filter.name}
                  </FilterItem>
                ))}
            </FilterContainer>
          )}
        </SelectedFiltersSection>
      )}
      
      {appliedFilters.length === 0 && (
        <>
          <FilterContainer>
            {filters.map(filter => (
              <FilterItem 
                key={filter.id} 
                onClick={() => handleFilterSelect(filter)}
              >
                {filter.name}
              </FilterItem>
            ))}
          </FilterContainer>
        </>
      )}
    </>
  );

  const renderRangeForm = () => (
    <FilterValueForm>
      <FilterHeader>Enter values to filter claims
        <CloseButton onClick={handleBackToMain}>
          <Icon type="close" color="#fff" />
        </CloseButton>
      </FilterHeader>
      <Underline />
      <FormInputsContainer>
        <div className="input-wrapper" style={{ width: '100%' }}>
          <input
            type="number"
            name="minValue"
            placeholder="Minimum value"
            value={minValue}
            onChange={(e) => setMinValue(e.target.value)}
            style={{
              height: '40px',
              width: '100%',
              padding: '8px 12px',
              borderRadius: '4px',
              border: '1px solid #4a5568',
              backgroundColor: '#2D3748',
              color: '#fff',
              fontSize: '16px',
              outline: 'none',
              boxSizing: 'border-box'
            }}
          />
        </div>
        <div className="input-wrapper" style={{ width: '100%' }}>
          <input
            type="number"
            name="maxValue"
            placeholder="Maximum value"
            value={maxValue}
            onChange={(e) => setMaxValue(e.target.value)}
            style={{
              height: '40px',
              width: '100%',
              padding: '8px 12px',
              borderRadius: '4px',
              border: '1px solid #4a5568',
              backgroundColor: '#2D3748',
              color: '#fff',
              fontSize: '16px',
              outline: 'none',
              boxSizing: 'border-box'
            }}
          />
        </div>
      </FormInputsContainer>
      <ContinueButton onClick={handleContinue}>CONTINUE</ContinueButton>
    </FilterValueForm>
  );

  const renderSelectView = () => (
    <StateSelectionContainer>
      <FilterHeader>
        Select {selectedFilter?.name.toLowerCase()}
        <CloseButton onClick={handleBackToMain}>
          <Icon type="close" color="#fff" />
        </CloseButton>
      </FilterHeader>
      <Underline />
      <StyledSearchInputWrapper>
        <SearchInput
          placeholder="Search State"
          value={searchTerm}
          onChange={handleSearchChange}
        />
      </StyledSearchInputWrapper>
      <StateList>
        {filteredOptions.map(option => (
          <StateItem 
            key={option.id} 
            onClick={() => handleStateSelect(option)}
          >
            {option.name}
            {option.count !== undefined && <StateCount>: {option.count}</StateCount>}
          </StateItem>
        ))}
      </StateList>
    </StateSelectionContainer>
  );

  return (
    <StyledWorkflowActionPanelFilters data-testid="workflow-action-panel-filters">
      {filterView === 'main' && (
        appliedFilters.length === 0 ? (
          <>
            {/* <FilterHeader>{label}</FilterHeader>
            <Underline /> */}
          </>
        ) : (
          <>
            <FilterHeader>Selected Filters</FilterHeader>
            <Underline />
          </>
        )
      )}
      {filterView === 'main' && renderMainView()}
      {filterView === 'form' && renderRangeForm()}
      {filterView === 'select' && renderSelectView()}
      {filterView === 'main' && appliedFilters.length > 0 && (
        <AddFilterButton onClick={() => setShowAvailableFilters(!showAvailableFilters)}>
          {showAvailableFilters ? 'HIDE FILTERS' : 'ADD FILTER'}
        </AddFilterButton>
      )}
    </StyledWorkflowActionPanelFilters>
  );
}