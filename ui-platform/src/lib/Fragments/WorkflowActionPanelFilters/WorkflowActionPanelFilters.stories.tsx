import React from 'react';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import { WorkflowActionPanelFilters } from './WorkflowActionPanelFilters';

export default {
  title: 'Fragments/WorkflowActionPanelFilters',
  component: WorkflowActionPanelFilters,
  parameters: {
    backgrounds: {
      default: 'dark',
      values: [
        { name: 'dark', value: '#1A202C' },
        { name: 'light', value: '#F7FAFC' },
      ],
    },
  },
} as ComponentMeta<typeof WorkflowActionPanelFilters>;

const Template: ComponentStory<typeof WorkflowActionPanelFilters> = (args) => (
  <div style={{ maxWidth: '400px', padding: '20px' }}>
    <WorkflowActionPanelFilters {...args} />
  </div>
);

export const Default = Template.bind({});
Default.args = {
  label: 'Filter Workflow',
  filters: [
    {
      id: 'job_value',
      name: 'Job Value',
      type: 'range',
    },
    {
      id: 'job_state',
      name: 'Job State',
      type: 'select',
      options: [
        { id: 'new', name: 'New', count: 12 },
        { id: 'in_progress', name: 'In Progress', count: 8 },
        { id: 'completed', name: 'Completed', count: 24 },
        { id: 'on_hold', name: 'On Hold', count: 3 },
        { id: 'cancelled', name: 'Cancelled', count: 5 },
      ],
    },
    {
      id: 'job_type',
      name: 'Job Type',
      type: 'select',
      options: [
        { id: 'repair', name: 'Repair', count: 18 },
        { id: 'installation', name: 'Installation', count: 15 },
        { id: 'maintenance', name: 'Maintenance', count: 22 },
        { id: 'inspection', name: 'Inspection', count: 7 },
      ],
    },
    {
      id: 'customer_type',
      name: 'Customer Type',
      type: 'select',
      options: [
        { id: 'residential', name: 'Residential', count: 35 },
        { id: 'commercial', name: 'Commercial', count: 27 },
        { id: 'government', name: 'Government', count: 8 },
      ],
    },
  ],
  onFilterApply: (filter) => console.log('Filter applied:', filter),
  onFilterRemove: (filterId) => console.log('Filter removed:', filterId),
};

export const WithPreselectedFilters = Template.bind({});
WithPreselectedFilters.args = {
  ...Default.args,
  filters: [
    {
      id: 'job_value',
      name: 'Job Value',
      type: 'range',
    },
    {
      id: 'job_state',
      name: 'Job State',
      type: 'select',
      options: [
        { id: 'new', name: 'New', count: 12 },
        { id: 'in_progress', name: 'In Progress', count: 8 },
        { id: 'completed', name: 'Completed', count: 24 },
        { id: 'on_hold', name: 'On Hold', count: 3 },
        { id: 'cancelled', name: 'Cancelled', count: 5 },
      ],
    },
    {
      id: 'priority',
      name: 'Priority',
      type: 'select',
      options: [
        { id: 'low', name: 'Low', count: 15 },
        { id: 'medium', name: 'Medium', count: 25 },
        { id: 'high', name: 'High', count: 10 },
        { id: 'urgent', name: 'Urgent', count: 5 },
      ],
    },
  ],
};

// This story demonstrates how to initialize the component with pre-selected filters
WithPreselectedFilters.decorators = [
  (Story) => {
    // We need to use React.useEffect to set the applied filters after the component mounts
    // This simulates a real-world scenario where filters might be loaded from a URL or state
    React.useEffect(() => {
      const filtersComponent = document.querySelector('[data-testid="workflow-action-panel-filters"]');
      if (filtersComponent) {
        // In a real implementation, you would use props or context to set initial filters
        // This is just for demonstration purposes in Storybook
        console.log('Component mounted - in a real app, you would set initial filters here');
      }
    }, []);
    return <Story />;
  },
];
