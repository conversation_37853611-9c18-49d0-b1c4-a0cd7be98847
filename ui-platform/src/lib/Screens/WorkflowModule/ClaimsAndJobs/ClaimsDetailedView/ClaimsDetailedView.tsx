import React, { useState } from 'react';
import {
  StateShell,
} from '../../../../Shells';
import { Pagination } from '../../../../Fragments/Pagination/Pagination';
import styled from 'styled-components';
import { ScrollableContent } from '../../../../Components/Scrollbar/Scrollbar';
import { useListPagination2 } from '../../../../Components';
import {  ClaimCardList, DefaultWorkflowFilters } from '../../../../Fragments';
import { WorkFlowFilterData } from '../../../../Components/Filter/WorkFlowFilter/WorkFlowFilter';
import { FilterSelectorItem } from '../../../../Components/Filter/Filter';
import { StaffMember } from '../../../../Auth';
import { AllInfo, StateConfig } from '../../../../Engine';

const getClaimsWorkflowConfig: () => StateConfig = () => {
  return {
    title: { 
        template: 'Payment preview completed {toUpperCase(todo.completed)} with userId of {foo.userId} count is {toUpperCase(todo.title)} with count {count(todo.title)}', 
        
    },
    fetchCalls: [
            {key: 'todos', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos'},
            {key: 'todo', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos/1'},
            {key: 'foo', method: 'GET', url: 'https://jsonplaceholder.typicode.com/todos/2'},
    ],
    onEnter: [],
    onLeave: [],
    defaultScreen: '',
    screens: {},
    actionPanels:  [
      {
        icon: "search-sm",
        title: "Search",
    
        fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: "WorkflowActionPanelSearch",
            layout: {},
            props: {
              searchUrl: "https://jsonplaceholder.typicode.com/todos",
              items: [],
              // setSearchResults: () => {},
              token: "",
              tokenPrefix: "",
            },
          }
        ],
        actionLevel: "topControls",
      },
      {
        icon: "close",
        title: "Filters",
    
        fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: "WorkflowActionPanelFilters",
            layout: {},
            props: {
              filters: [
                {
                  id: 'job_value',
                  name: 'Job Value',
                  type: 'range',
                },
                {
                  id: 'job_state',
                  name: 'Job State',
                  type: 'select',
                  options: [
                    { id: 'new', name: 'New', count: 12 },
                    { id: 'in_progress', name: 'In Progress', count: 8 },
                    { id: 'completed', name: 'Completed', count: 24 },
                    { id: 'on_hold', name: 'On Hold', count: 3 },
                    { id: 'cancelled', name: 'Cancelled', count: 5 },
                  ],
                },
                {
                  id: 'job_type',
                  name: 'Job Type',
                  type: 'select',
                  options: [
                    { id: 'repair', name: 'Repair', count: 18 },
                    { id: 'installation', name: 'Installation', count: 15 },
                    { id: 'maintenance', name: 'Maintenance', count: 22 },
                    { id: 'inspection', name: 'Inspection', count: 7 },
                  ],
                },
                {
                  id: 'customer_type',
                  name: 'Customer Type',
                  type: 'select',
                  options: [
                    { id: 'residential', name: 'Residential', count: 35 },
                    { id: 'commercial', name: 'Commercial', count: 27 },
                    { id: 'government', name: 'Government', count: 8 },
                  ],
                },
              ],
              label: "Filter Workflow",
              // onFilterApply: () => {},
              // onFilterRemove: () => {},
              // _onNotify: () => {},
            },
          }
        ],
        actionLevel: "topControls",
      },
      {
        icon: "building-04",
        title: "Buckets",
    
        fetchCalls: [],
        layout: {},
        onEnter: [],
        onLeave: [],
        fragments: [
          {
            component: "WorkflowActionPanelBuckets",
            layout: {},
            props: {
              buckets: [
                {
                  id: 'photos',
                  name: 'Photos',
                  filterCondition: {
                    name: 'documentType',
                    key: 'type',
                    value: 'photo',
                    operator: 'equals'
                  }
                },
                {
                  id: 'quotations',
                  name: 'Quotations',
                  filterCondition: {
                    name: 'documentType',
                    key: 'type',
                    value: 'quotation',
                    operator: 'equals'
                  }
                },
                {
                  id: 'invoices',
                  name: 'Invoices',
                  filterCondition: {
                    name: 'documentType',
                    key: 'type',
                    value: 'invoice',
                    operator: 'equals'
                  }
                },
                {
                  id: 'reports',
                  name: 'Reports',
                  filterCondition: {
                    name: 'documentType',
                    key: 'type',
                    value: 'report',
                    operator: 'equals'
                  }
                },
                {
                  id: 'latest',
                  name: 'Latest Documents',
                  filterCondition: {
                    name: 'latestDocuments',
                    key: 'createdAt',
                    value: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
                    operator: 'greaterThan'
                  }
                }
              ],
              label: "Bucket Filter",
            }
          }
        ],
        actionLevel: "topControls",
      },
        {
          icon: 'bell-02',
          title: 'Messages', //?actionPanel=Messages--bell-02
    
          fetchCalls: [],
          layout: {},
          onEnter: [],
          onLeave: [],
          fragments: [
            {
              component: 'NoteCardList',
              layout: {
                marginTop: '20px',
                marginLeft: '10px',
                marginRight: '10px',
              },
              props: {
                notes: [
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                ],
              },
            },
          ],
          actionLevel: 'bottomControls',
        },
        {
          icon: 'trash-01',
          title: 'Scratch Pad', //?actionPanel=Messages--bell-02
    
          fetchCalls: [],
          layout: {},
          onEnter: [],
          onLeave: [],
          fragments: [
            {
              component: 'NoteCardList',
              layout: {
                marginTop: '20px',
                marginBottom: '20px',
                marginLeft: '10px',
                marginRight: '10px',
              },
              props: {
                notes: [
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                  {
                    title: 'Find Invoice',
                    date: '05/10/23',
                    time: '11:42',
                    content:
                      'Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. Dorem ipsum dolor sit amet, consectetur adipiscing elit. ',
                  },
                ],
              },
            },
          ],
          actionLevel: 'bottomControls',
        },
      ],
}
};


const ModuleContent = styled.div`
  background: ${(props) => props.theme.ColorsBackgroundModule};
  padding-top: 2rem;
  padding-left: 1rem;
  padding-right: 1rem;
  box-sizing: border-box;
  border-radius: 0 7px 7px 7px;
  grid-area: Module-Content;
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
`;

const FiltersWrapper = styled.div`
  position: relative;
  z-index: 2;
`;

const JobListWrapper = styled.div`
  position: relative;
  z-index: 1;
  margin-top: 1rem;
`;

const ViewShellPagination = styled(Pagination)`
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
`;

const Content = styled(ScrollableContent)<{
  items?: any[];
  scrollable?: boolean;
}>`
  max-height: 100% !important;
  padding-right: ${(props) => props.theme.SpacingXl};
  height: calc(100vh - 2rem - 72px);

  ${(props) =>
    props?.scrollable || (props?.items && props?.items.length > 0)
      ? 'padding-bottom: 4rem; box-sizing: border-box'
      : ''};
  ${(props) => (!props?.scrollable ? 'overflow: hidden;' : '')};
`;



interface Props {
  filtersData: (WorkFlowFilterData)[];
  filterMenuData: FilterSelectorItem[];
  claims: any[];
  staffMember: StaffMember;
  allInfo: Partial<AllInfo>;
  itemsPerPage?: number;
  jobCardNumberPrefix: string;
  searchUrl?: string;
  ClaimLinkRouter: any;
  JobLinkRouter: any;
  tokenPrefix?: string;
  token?: string;
  getClaimMenuItems: (claim: any) => {icon: string; label: string; path: string}[];
  getJobMenuItems: (job: any) => {icon: string; label: string; path: string}[];
}

export function ClaimsDetailedView({ ClaimLinkRouter, JobLinkRouter, searchUrl, filtersData, filterMenuData, claims, staffMember, allInfo, itemsPerPage, token, tokenPrefix, getClaimMenuItems, getJobMenuItems}: Props) {
  const [filteredClaims, setFilteredClaims] = useState<any[]>([]);
  const { pages, currentPage, pageItems, ...rest } = useListPagination2({
    items: filteredClaims || [],
    itemsPerPage: itemsPerPage || 10,
  });
  
  return (
      <StateShell
        callClientAction={() => {}}
        stateConfig={getClaimsWorkflowConfig()}
        clientDataObject={{}}
        fakeUseNavigation={{ state: 'idle' }}
      >
        <ModuleContent data-testid="workflow-view-shell-module-content">
          <Content
            data-testid="workflow-view-shell-content"
            items={pageItems}
            scrollable={true}
          >
            {/* <FiltersWrapper>
              <DefaultWorkflowFilters 
                filtersData={filtersData} 
                filterMenuData={filterMenuData} 
                searchUrl={searchUrl || ''}
                items={claims} 
                setFilteredItems={setFilteredClaims} 
                token={token}
                tokenPrefix={tokenPrefix} />
            </FiltersWrapper> */}
            <JobListWrapper>
              <ClaimCardList displayLogo={false} claims={pageItems} staffMember={staffMember} allInfo={allInfo} getClaimMenuItems={getClaimMenuItems} getJobMenuItems={getJobMenuItems} ClaimLinkRouter={ClaimLinkRouter} JobLinkRouter={JobLinkRouter} />
            </JobListWrapper>
          </Content>
          <ViewShellPagination
            pages={pages}
            currentPage={currentPage}
            {...rest}
          />
        </ModuleContent>
      </StateShell>
  );
}
